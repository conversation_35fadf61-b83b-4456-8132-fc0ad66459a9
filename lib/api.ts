import axios from 'axios';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL ? `${process.env.NEXT_PUBLIC_API_URL}/api/users` : 'http://localhost:3003/api/users';
const LAVNI_PRODUCTION_API = 'https://api.lavnihealth.com/api/public';

export interface Therapist {
  _id: string;
  name: string;
  title: string;
  image: string;
  specialties: string[];
  licensedStates: string[];
  nextAvailability: string;
  bio: string;
  videoUrl: string;
  gender: string;
  experience: string;
  ethnicity: string;
  adminApproved: boolean;
  insuranceCompanies: string[];
}

export interface AppointmentData {
  therapistId: string;
  selectedMonth: string;
  appointments?: any[];
  workingHours?: any[];
  blockedDates?: any[];
}

// Dữ liệu mẫu để sử dụng khi không thể kết nối với API
const mockTherapists: Therapist[] = [
  {
    _id: "1",
    name: "Dr. <PERSON>",
    title: "Licensed Psychologist",
    image: "/placeholder.svg",
    specialties: ["Anxiety", "Depression", "Trauma", "PTSD"],
    licensedStates: ["CA", "NY"],
    nextAvailability: "Tomorrow, 2PM",
    bio: "Dr. <PERSON> is a licensed psychologist with over 10 years of experience helping clients overcome anxiety, depression, and trauma.",
    videoUrl: "",
    gender: "Female",
    experience: "10+ years",
    ethnicity: "Asian",
    adminApproved: true,
    insuranceCompanies: [],
  },
  {
    _id: "2",
    name: "Dr. James Wilson",
    title: "Clinical Psychologist",
    image: "/placeholder.svg",
    specialties: ["Stress", "Relationships", "Work-Life Balance"],
    licensedStates: ["CA", "TX"],
    nextAvailability: "Thursday, 10AM",
    bio: "Dr. Wilson specializes in helping professionals manage stress and improve work-life balance.",
    videoUrl: "",
    gender: "Male",
    experience: "5-10 years",
    ethnicity: "Caucasian",
    adminApproved: true,
    insuranceCompanies: [],
  },
  {
    _id: "3",
    name: "Sarah Johnson, LMFT",
    title: "Licensed Marriage & Family Therapist",
    image: "/placeholder.svg",
    specialties: ["Couples Therapy", "Family Conflict", "Parenting"],
    licensedStates: ["CA"],
    nextAvailability: "Friday, 4PM",
    bio: "Sarah helps couples and families improve communication and resolve conflicts.",
    videoUrl: "",
    gender: "Female",
    experience: "3-5 years",
    ethnicity: "African American",
    adminApproved: true,
    insuranceCompanies: [],
  }
];

const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

export const therapistApi = {
  async getAllTherapists(
    state?: string, 
    insurance?: string, 
    genderFilters?: string[], 
    ethnicityFilters?: string[]
  ): Promise<Therapist[]> {
    try {
      const params = new URLSearchParams();
      if (state) params.append('state', state);
      if (insurance) params.append('insurance', insurance);
      if (genderFilters && genderFilters.length > 0) {
        params.append('gender', genderFilters.join(','));
      }
      if (ethnicityFilters && ethnicityFilters.length > 0) {
        params.append('ethnicity', ethnicityFilters.join(','));
      }
      
      const queryString = params.toString();
      const url = `${API_BASE_URL}/therapists${queryString ? `?${queryString}` : ''}`;
      
      console.log('Fetching therapists from:', url);
      const response = await api.get(`/therapists${queryString ? `?${queryString}` : ''}`);
      return response.data.data.therapists;
    } catch (error) {
      console.error('Error fetching therapists:', error);
      // Return mock data as fallback
      return mockTherapists;
    }
  },

  getTherapistById: async (id: string): Promise<Therapist> => {
    try {
      const response = await api.get(`/therapists/${id}`);
      return response.data.data.therapist;
    } catch (error) {
      console.error('Error fetching therapist, using mock data instead:', error);
      // Trả về dữ liệu mẫu nếu API gọi thất bại
      const mockTherapist = mockTherapists.find(t => t._id === id);
      if (mockTherapist) return mockTherapist;
      return mockTherapists[0];
    }
  },

  // New function to call production Lavni Health API
  async getTherapistAppointments(
    therapistId: string, 
    selectedMonth: string, 
    authToken?: string
  ): Promise<AppointmentData> {
    try {
      const headers: Record<string, string> = {
        'Accept': 'application/json, text/plain, */*',
        'Content-Type': 'application/json',
        'Origin': 'https://mylavni.com',
        'User-Agent': 'Lavni-Client/1.0'
      };

      if (authToken) {
        headers['Authorization'] = authToken.startsWith('Bearer ') ? authToken : `Bearer ${authToken}`;
      }

      console.log('Fetching appointments from Lavni Health API for therapist:', therapistId);
      
      const response = await fetch(`${LAVNI_PRODUCTION_API}/viewAllAppointmentsByTherapistIdForMonth`, {
        method: 'POST',
        headers,
        body: JSON.stringify({
          therapistId,
          selectedMonth
        })
      });

      if (!response.ok) {
        throw new Error(`API responded with status: ${response.status}`);
      }

      const data = await response.json();
      console.log('Successfully fetched appointment data from Lavni Health API');
      return data;
    } catch (error) {
      console.error('Error fetching appointments from Lavni Health API:', error);
      // Fallback to local API
      console.log('Falling back to local API...');
      return this.getLocalTherapistData(therapistId, selectedMonth);
    }
  },

  // Fallback to your existing local API
  async getLocalTherapistData(therapistId: string, month: string): Promise<AppointmentData> {
    try {
      const response = await fetch(`/api/therapists/${therapistId}/available-days?month=${month}`);
      const data = await response.json();
      return {
        therapistId,
        selectedMonth: month,
        ...data
      };
    } catch (error) {
      console.error('Error fetching local therapist data:', error);
      throw error;
    }
  },

  // Get therapist details including working hours and blocked dates
  async getTherapistDetails(therapistId: string): Promise<{ workingHours: any[], blockedDates: any[] }> {
    try {
      const headers: Record<string, string> = {
        'Accept': 'application/json, text/plain, */*',
        'Content-Type': 'application/json',
        'Origin': 'https://mylavni.com',
        'User-Agent': 'Lavni-Client/1.0'
      };

      console.log('Fetching therapist details from Lavni Health API for therapist:', therapistId);

      // Use the correct API that has blockedDates field
      const response = await fetch(`${LAVNI_PRODUCTION_API}/getTherapistByUserIdLimited/${therapistId}`, {
        method: 'GET',
        headers
      });

      if (!response.ok) {
        throw new Error(`API responded with status: ${response.status}`);
      }

      const data = await response.json();

      if (data.success && data.data) {
        console.log(`[API] Successfully fetched therapist details with ${data.data.blockedDates?.length || 0} blocked dates`);
        return {
          workingHours: data.data.workingHours || [],
          blockedDates: data.data.blockedDates || []
        };
      } else {
        console.log('Could not fetch therapist details from production API');
        return {
          workingHours: [],
          blockedDates: []
        };
      }
    } catch (error) {
      console.error('Error fetching therapist details from Lavni Health API:', error);

      // No fallback to local API or mock data - return empty if production API fails
      return {
        workingHours: [],
        blockedDates: []
      };
    }
  }
};

export default api; 