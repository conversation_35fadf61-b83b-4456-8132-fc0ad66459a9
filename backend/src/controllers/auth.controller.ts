import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import { User, IUser } from '../models/User';
import { AppError } from '../middleware/error.middleware';
import { config } from '../config/environment';

const generateToken = (userId: string): string => {
  return jwt.sign({ id: userId }, config.jwtSecret, {
    expiresIn: '30d',
  });
};

export const register = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    // Log registration attempt (minus password)
    const { email, firstname, lastname, role, dateOfBirth } = req.body;
    console.log(`[REGISTER] Attempt: email=${email}, firstname=${firstname}, lastname=${lastname}, role=${role}, dateOfBirth=${dateOfBirth}`);

    // Check if user already exists
    const existingUser = await User.findOne({ email });
    if (existingUser) {
      const logMsg = `[REGISTER] Registration failed: <PERSON><PERSON> already registered (${email}), firstname=${firstname}, lastname=${lastname}, role=${role}, dateOfBirth=${dateOfBirth}`;
      console.error(logMsg);
      throw new AppError('Email already registered', 400);
    }

    // Create new user
    const user = await User.create({
      email,
      password: req.body.password,
      firstname,
      lastname,
      role,
      dateOfBirth,
    }) as IUser;

    // Generate token
    const token = generateToken(user._id.toString());

    console.log(`[REGISTER] Success: userId=${user._id}, email=${email}`);

    res.status(201).json({
      status: 'success',
      token,
      data: {
        user: {
          id: user._id,
          email: user.email,
          firstname: user.firstname,
          lastname: user.lastname,
          role: user.role,
        },
      },
    });
  } catch (error) {
    // Log all errors with request context
    const { email, firstname, lastname, role, dateOfBirth } = req.body || {};
    console.error('[REGISTER] Error:', {
      error,
      email,
      firstname,
      lastname,
      role,
      dateOfBirth
    });
    next(error);
  }
};

export const login = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const { email, password } = req.body;

    // Find user
    const user = await User.findOne({ email }).select('+password') as IUser;
    if (!user) {
      throw new AppError('Invalid email or password', 401);
    }

    // Check password
    const isPasswordValid = await user.comparePassword(password);
    if (!isPasswordValid) {
      throw new AppError('Invalid email or password', 401);
    }

    // Generate token
    const token = generateToken(user._id.toString());

    res.status(200).json({
      status: 'success',
      token,
      data: {
        user: {
          id: user._id,
          email: user.email,
          firstname: user.firstname,
          lastname: user.lastname,
          role: user.role,
        },
      },
    });
  } catch (error) {
    next(error);
  }
};

export const verifyEmail = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const { token } = req.body;

    // Verify token and update user
    const decoded = jwt.verify(token, config.jwtSecret) as { id: string };
    const user = await User.findByIdAndUpdate(
      decoded.id,
      { verifiedStatus: 'VERIFIED' },
      { new: true }
    );

    if (!user) {
      throw new AppError('Invalid verification token', 400);
    }

    res.status(200).json({
      status: 'success',
      message: 'Email verified successfully',
    });
  } catch (error) {
    next(error);
  }
};

export const googleAuth = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const { googleId, email, firstname, lastname } = req.body;

    let user = await User.findOne({ googleId }) as IUser;

    if (!user) {
      // Create new user if doesn't exist
      user = await User.create({
        googleId,
        email,
        firstname,
        lastname,
        role: 'CLIENT', // Default role for Google auth
        verifiedStatus: 'VERIFIED', // Auto verify Google users
        medium: 'GOOGLE',
      }) as IUser;
    }

    // Generate token
    const token = generateToken(user._id.toString());

    res.status(200).json({
      status: 'success',
      token,
      data: {
        user: {
          id: user._id,
          email: user.email,
          firstname: user.firstname,
          lastname: user.lastname,
          role: user.role,
        },
      },
    });
  } catch (error) {
    next(error);
  }
}; 