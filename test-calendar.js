const fetch = require('node-fetch');

// Get API URL from environment variable or use default
const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3003';
const FRONTEND_URL = 'http://localhost:3000';

async function testCalendarAPI() {
  console.log('🧪 Testing Calendar API Endpoints...\n');
  console.log(`Using API URL: ${API_URL}`);
  console.log(`Using Frontend URL: ${FRONTEND_URL}\n`);

  // Test 1: Check if backend is running
  try {
    console.log('1. Testing backend connection...');
    const backendResponse = await fetch(`${API_URL}/api/users/therapists`);
    const therapists = await backendResponse.json();
    console.log(`✅ Backend connected - Found ${therapists.data.therapists.length} therapists\n`);
  } catch (error) {
    console.log('❌ Backend connection failed:', error.message);
    return;
  }

  // Test 2: Check if frontend is running
  try {
    console.log('2. Testing frontend connection...');
    const frontendResponse = await fetch(FRONTEND_URL);
    console.log(`✅ Frontend connected - Status: ${frontendResponse.status}\n`);
  } catch (error) {
    console.log('❌ Frontend connection failed:', error.message);
    return;
  }

  // Test 3: Test therapist availability API
  try {
    console.log('3. Testing therapist availability API...');
    const therapistId = '675c597705d202e19eab9d06'; // Beth Philipsen
    const month = '2025-05';

    const availabilityResponse = await fetch(`${FRONTEND_URL}/api/therapists/${therapistId}/available-days?month=${month}`);
    const availabilityData = await availabilityResponse.json();
    
    console.log(`✅ Availability API working - Status: ${availabilityResponse.status}`);
    console.log(`   Working hours: ${availabilityData.workingHours?.length || 0} periods`);
    console.log(`   Blocked dates: ${availabilityData.blockedDates?.length || 0} blocks`);
    console.log(`   Appointments: ${availabilityData.appointments?.length || 0} appointments\n`);
  } catch (error) {
    console.log('❌ Availability API failed:', error.message);
  }

  // Test 4: Test insurance filtering
  try {
    console.log('4. Testing insurance filtering...');
    const insuranceResponse = await fetch(`${API_URL}/api/users/therapists?state=North+Carolina`);
    const insuranceData = await insuranceResponse.json();
    
    console.log(`✅ Insurance filtering working - Found ${insuranceData.data.therapists.length} therapists in NC`);
    
    // Check if therapists have insurance data
    const therapistsWithInsurance = insuranceData.data.therapists.filter(t => t.insuranceCompanies && t.insuranceCompanies.length > 0);
    console.log(`   Therapists with insurance: ${therapistsWithInsurance.length}\n`);
  } catch (error) {
    console.log('❌ Insurance filtering failed:', error.message);
  }

  console.log('🎉 Testing complete!');
}

// Run the test
testCalendarAPI().catch(console.error); 