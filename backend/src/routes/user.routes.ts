import { Router } from 'express';
import { 
  getProfile, 
  updateProfile, 
  getAllTherapists, 
  getTherapistById,
  updateTherapistAvailability
} from '../controllers/user.controller';
import { authenticate } from '../middleware/auth.middleware';

const router = Router();

// Public routes - no authentication needed
router.get('/therapists', getAllTherapists);
router.get('/therapists/:id', getTherapistById);

// Protected routes - require authentication
router.get('/profile', authenticate, getProfile);
router.put('/profile', authenticate, updateProfile);
router.put('/therapists/availability', authenticate, updateTherapistAvailability);

export default router; 