import { useCallback } from 'react';

interface GTMEvent {
  event: string;
  [key: string]: any;
}

export const useGTM = () => {
  const pushToDataLayer = useCallback((eventData: GTMEvent) => {
    if (typeof window !== 'undefined' && window.dataLayer) {
      window.dataLayer.push(eventData);
    }
  }, []);

  // Onboarding specific events
  const trackOnboardingStep = useCallback((step: number, stepName: string) => {
    pushToDataLayer({
      event: 'onboarding_step',
      step_number: step,
      step_name: stepName,
      page_path: window.location.pathname
    });
  }, [pushToDataLayer]);

  const trackFormSubmit = useCallback((formName: string, success: boolean, data?: any) => {
    pushToDataLayer({
      event: 'form_submit',
      form_name: formName,
      success,
      form_data: data,
      page_path: window.location.pathname
    });
  }, [pushToDataLayer]);

  const trackButtonClick = useCallback((buttonName: string, location?: string) => {
    pushToDataLayer({
      event: 'button_click',
      button_name: buttonName,
      button_location: location || window.location.pathname,
      page_path: window.location.pathname
    });
  }, [pushToDataLayer]);

  const trackUserSelection = useCallback((category: string, selection: string | string[]) => {
    pushToDataLayer({
      event: 'user_selection',
      selection_category: category,
      selection_value: Array.isArray(selection) ? selection.join(',') : selection,
      page_path: window.location.pathname
    });
  }, [pushToDataLayer]);

  const trackConversion = useCallback((conversionType: string, value?: number) => {
    pushToDataLayer({
      event: 'conversion',
      conversion_type: conversionType,
      conversion_value: value,
      page_path: window.location.pathname
    });
  }, [pushToDataLayer]);

  return {
    pushToDataLayer,
    trackOnboardingStep,
    trackFormSubmit,
    trackButtonClick,
    trackUserSelection,
    trackConversion
  };
}; 