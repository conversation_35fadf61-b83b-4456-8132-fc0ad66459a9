import moment from 'moment';

export interface WorkingHours {
  day: string;
  startTime: string;
  endTime: string;
}

export interface BlockedDate {
  start: string | Date;
  end: string | Date;
}

export interface Appointment {
  start: string | Date;
  end: string | Date;
  status: string;
}

export interface TherapistData {
  workingHours: WorkingHours[];
  blockedDates: BlockedDate[];
  appointments: Appointment[];
}

// Helper function to convert day name to number
export const dayOfWeekAsNumber = (day: string): number => {
  const dayMap: Record<string, number> = {
    'Sunday': 0,
    'Monday': 1,
    'Tuesday': 2,
    'Wednesday': 3,
    'Thursday': 4,
    'Friday': 5,
    'Saturday': 6
  };
  return dayMap[day];
};

// Convert UTC date to local date (no offset applied)
export const convertUTCDateToLocalDate = (timeStr: string): string => {
  console.log('[DEBUG] convertUTCDateToLocalDate input:', timeStr);
  
  // If it's already in AM/PM format, return as-is
  if (timeStr.includes('AM') || timeStr.includes('PM')) {
    console.log('[DEBUG] Already in AM/PM format:', timeStr);
    return timeStr;
  }
  
  // If it's in HH:mm format, convert to 12-hour format
  if (/^\d{2}:\d{2}$/.test(timeStr)) {
    const [hour, minute] = timeStr.split(':').map(Number);
    console.log('[DEBUG] Parsed HH:mm format - hour:', hour, 'minute:', minute);
    
    const date = new Date();
    date.setHours(hour, minute, 0, 0);
    
    const result = date.toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true
    });
    console.log('[DEBUG] Converted to 12-hour format:', result);
    return result;
  }
  
  // For ISO strings or other formats, parse and convert (no offset)
  try {
    const date = new Date(timeStr);
    console.log('[DEBUG] Parsed ISO date:', date.toISOString());
    
    const result = date.toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true
    });
    console.log('[DEBUG] Final result:', result);
    return result;
  } catch (error) {
    console.error('Error converting time:', timeStr, error);
    return timeStr;
  }
};

// Helper function to check if date is in blocked range
export const isDateInBlockedRange = (start: moment.Moment, end: moment.Moment, date: moment.Moment): boolean => {
  return date.isBetween(start, end, null, '[]') || 
         date.isSame(start) || 
         date.isSame(end);
};

// Calculate all available dates for multiple months
export const calculateAllAvailableDates = (
  therapistData: TherapistData,
  currentYear: number,
  currentMonth: number,
  monthsToCheck: number = 3
): { date: Date; day: number; month: number; year: number }[] => {
  const result: { date: Date; day: number; month: number; year: number }[] = [];
  const today = new Date();
  const tomorrowDate = moment(today).add(1, "day").startOf("day").toDate();

  // Process working hours
  const availableHours: any[] = [];
  
  if (therapistData.workingHours?.length) {
    therapistData.workingHours.forEach((obj: any) => {
      const dayAsNumber = dayOfWeekAsNumber(obj.day);

      if (convertUTCDateToLocalDate(obj.endTime) > convertUTCDateToLocalDate(obj.startTime)) {
        availableHours.push({
          startTime: convertUTCDateToLocalDate(obj.startTime),
          endTime: convertUTCDateToLocalDate(obj.endTime),
          daysOfWeek: [dayAsNumber],
        });
      } else {
        // Handle overnight shifts
        availableHours.push({
          startTime: convertUTCDateToLocalDate(obj.startTime),
          endTime: "24:00 AM",
          daysOfWeek: [dayAsNumber],
        });

        availableHours.push({
          startTime: "00:00 AM",
          endTime: convertUTCDateToLocalDate(obj.endTime),
          daysOfWeek: [(dayAsNumber + 1) % 7],
        });
      }
    });
  } else {
    availableHours.push({
      startTime: "00:00 AM",
      endTime: "00:00 PM",
      daysOfWeek: [10], // Non-existent day
    });
  }

  // Calculate for specified number of months
  for (let monthOffset = 0; monthOffset < monthsToCheck; monthOffset++) {
    const year = currentMonth + monthOffset > 11
      ? currentYear + 1
      : currentYear;
    const month = (currentMonth + monthOffset) % 12;

    // Get number of days in month
    const daysInMonth = getDaysInMonth(year, month);

    // Filter blocked dates for this month
    const filteredBlockedDates = therapistData.blockedDates.filter((item: any) => {
      const startMonth = moment(item.start).month();
      const startYear = moment(item.start).year();
      const endMonth = moment(item.end).month();
      const endYear = moment(item.end).year();

      return (startMonth === month && startYear === year) ||
             (endMonth === month && endYear === year);
    });

    // Filter appointments for this month
    const filteredAppointments = therapistData.appointments.filter((item: any) => {
      const startMonth = moment(item.start).month();
      const startYear = moment(item.start).year();
      const endMonth = moment(item.end).month();
      const endYear = moment(item.end).year();

      return (startMonth === month && startYear === year) ||
             (endMonth === month && endYear === year);
    });

    // Check each day in the month
    for (let day = 1; day <= daysInMonth; day++) {
      const date = new Date(year, month, day);

      // Only check future dates
      if (moment(date).isBefore(tomorrowDate)) {
        continue;
      }

      // Don't check dates too far in the future
      if (moment(date).isAfter(moment().add(60, 'days'))) {
        continue;
      }

      // Check if this day has available time slots
      const dayOfWeek = date.getDay();
      const availableTimeSlots = getAvailableTimesForDate(
        date,
        dayOfWeek,
        availableHours,
        filteredBlockedDates,
        filteredAppointments
      );

      // Only add days that have at least 1 available slot
      if (availableTimeSlots.length > 0) {
        result.push({
          date: new Date(year, month, day),
          day,
          month,
          year
        });
      }
    }
  }

  // Sort dates in ascending order
  result.sort((a, b) => a.date.getTime() - b.date.getTime());

  return result;
};

// Get available times for a specific date
export const getAvailableTimesForDate = (
  date: Date,
  dayOfWeek: number,
  availableHours: any[],
  blockedDates: any[],
  appointments: any[]
): string[] => {
  // Get available time slots for this day of week
  const availableTimeSlots: string[][] = [];

  availableHours
    .filter((obj) => obj.daysOfWeek[0] === dayOfWeek)
    .forEach((obj) => {
      const availableHourSingle: string[] = [];

      const startHourMatch = obj.startTime.match(/(\d+):/);
      const endHourMatch = obj.endTime.match(/(\d+):/);

      if (startHourMatch && endHourMatch) {
        const startHour = parseInt(startHourMatch[1]);
        const endHour = parseInt(endHourMatch[1]);

        for (let hour = startHour; hour <= endHour; hour++) {
          if (hour === startHour) {
            const mST = moment(obj.startTime, "HH:mm").minute();
            if (mST === 0) {
              availableHourSingle.push(moment({ hour }).format("h:mm A"));
            }
          } else {
            availableHourSingle.push(moment({ hour }).format("h:mm A"));
          }

          if (hour !== endHour) {
            availableHourSingle.push(
              moment({
                hour,
                minute: 30,
              }).format("h:mm A")
            );
          } else {
            const mET = moment(obj.endTime, "HH:mm").minute();
            if (mET === 30) {
              availableHourSingle.push(
                moment({
                  hour,
                  minute: 30,
                }).format("h:mm A")
              );
            }
          }
        }
      }

      availableTimeSlots.push(availableHourSingle);
    });

  // Create all available slots
  let allAvailableSlots: string[] = [];

  availableTimeSlots.forEach((slotArray: string[]) => {
    slotArray.forEach((slot: string) => {
      const slotTime = moment(slot, "h:mm A");
      const slotDate = moment(date).hours(slotTime.hours()).minutes(slotTime.minutes()).seconds(0);
      const endTime = moment(slotDate).add(60, "minutes");

      // Check if both start and end times are within working hours
      if (
        moment(date).isAfter(moment().startOf('day')) && // Only future dates
        slotArray.includes(moment(slotDate).format("h:mm A")) &&
        slotArray.includes(moment(endTime).format("h:mm A"))
      ) {
        allAvailableSlots.push(slot);
      }
    });
  });

  // Remove duplicates
  allAvailableSlots = [...new Set(allAvailableSlots)];

  // Filter out blocked slots and appointments
  allAvailableSlots = allAvailableSlots.filter(slot => {
    const slotTime = moment(slot, "h:mm A");
    const slotStart = moment(date).hours(slotTime.hours()).minutes(slotTime.minutes()).seconds(0);
    const slotEnd = slotStart.clone().add(60, "minutes"); // 1-hour appointment duration

    // Check if slot overlaps with blocked dates
    const isInBlockedRange = blockedDates.some((range: any) => {
      const blockStart = moment(range.start);
      const blockEnd = moment(range.end);
      
      // Check if slot overlaps with blocked time
      // Overlap if: slotStart < blockEnd AND slotEnd > blockStart
      return slotStart.isBefore(blockEnd) && slotEnd.isAfter(blockStart);
    });

    if (isInBlockedRange) {
      return false;
    }

    // Check if slot overlaps with appointments
    const appointmentAlreadyExists = appointments.some((appointment: any) => {
      const aptStart = moment(appointment.start);
      const aptEnd = moment(appointment.end);
      
      // Check if slot overlaps with appointment time
      // Overlap if: slotStart < aptEnd AND slotEnd > aptStart
      return slotStart.isBefore(aptEnd) && slotEnd.isAfter(aptStart);
    });

    return !isInBlockedRange && !appointmentAlreadyExists;
  });

  // Sort slots by time
  allAvailableSlots.sort((a, b) => {
    return moment(a, "h:mm A").valueOf() - moment(b, "h:mm A").valueOf();
  });

  return allAvailableSlots;
};

// Helper function to get days in month
export const getDaysInMonth = (year: number, month: number): number => {
  return new Date(year, month + 1, 0).getDate();
}; 