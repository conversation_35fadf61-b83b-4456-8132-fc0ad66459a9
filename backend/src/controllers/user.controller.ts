import { Request, Response, NextFunction } from 'express';
import { User } from '../models/User';
import { AppError } from '../middleware/error.middleware';
import { Upload } from '../models/Upload';
import { ExperienceTag } from '../models/ExperienceTag';
import { Appointment } from '../models/Appointment';
import { calculateNextAvailability } from '../utils/availability';
import { InsuranceCompany } from '../models/InsuranceCompany';
import { Ethnicity } from '../models/Ethnicity';

export const getProfile = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const user = await User.findById(req.user._id).select('-password');
    res.status(200).json({
      status: 'success',
      data: { user },
    });
  } catch (error) {
    next(error);
  }
};

export const updateProfile = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const updatedUser = await User.findByIdAndUpdate(
      req.user._id,
      {
        $set: {
          firstname: req.body.firstname,
          lastname: req.body.lastname,
          city: req.body.city,
          state: req.body.state,
          gender: req.body.gender,
          primaryPhone: req.body.primaryPhone,
          description: req.body.description,
        },
      },
      { new: true, runValidators: true }
    ).select('-password');

    res.status(200).json({
      status: 'success',
      data: { user: updatedUser },
    });
  } catch (error) {
    next(error);
  }
};

function stripHtml(html: string = ''): string {
  return html.replace(/<[^>]+>/g, '');
}

function htmlToPlainText(html = '') {
  // Replace block tags with newlines
  let text = html
    .replace(/<\/?(p|br|li|ul|ol|h[1-6])[^>]*>/gi, '\n')
    .replace(/&nbsp;/gi, ' ')
    .replace(/&amp;/gi, '&')
    .replace(/&quot;/gi, '"')
    .replace(/&lt;/gi, '<')
    .replace(/&gt;/gi, '>');
  // Remove all other tags
  text = text.replace(/<[^>]+>/g, '');
  // Replace multiple newlines with a single newline
  text = text.replace(/\n+/g, '\n');
  // Trim
  return text.trim();
}

export const getAllTherapists = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    console.log('Starting getAllTherapists query...');
    const { state, insurance, concern, gender, ethnicity } = req.query;
    console.log('State filter:', state);
    console.log('Insurance filter:', insurance);
    console.log('Concern filter:', concern);
    console.log('Gender filter:', gender);
    console.log('Ethnicity filter:', ethnicity);
    
    // Parse concerns - can be a comma-separated string or array
    let concerns: string[] = [];
    if (concern) {
      if (Array.isArray(concern)) {
        concerns = concern as string[];
      } else if (typeof concern === 'string') {
        concerns = concern.split(',').map(c => c.trim()).filter(c => c.length > 0);
      }
    }
    console.log('Parsed concerns:', concerns);

    // Parse gender filters - can be a comma-separated string or array
    let genderFilters: string[] = [];
    if (gender) {
      if (Array.isArray(gender)) {
        genderFilters = gender as string[];
      } else if (typeof gender === 'string') {
        genderFilters = gender.split(',').map(g => g.trim()).filter(g => g.length > 0);
      }
    }
    console.log('Parsed gender filters:', genderFilters);

    // Parse ethnicity filters - can be a comma-separated string or array
    let ethnicityFilters: string[] = [];
    if (ethnicity) {
      if (Array.isArray(ethnicity)) {
        ethnicityFilters = ethnicity as string[];
      } else if (typeof ethnicity === 'string') {
        ethnicityFilters = ethnicity.split(',').map(e => e.trim()).filter(e => e.length > 0);
      }
    }
    console.log('Parsed ethnicity filters:', ethnicityFilters);
    
    // Symptom to tag mapping
    const symptomToTag: { [key: string]: string[] } = {
      "Changes in Mood": ["Bipolar and Related Disorders", "Depressive Disorders"],
      "Difficulty Sleeping": ["Trauma and Stressor Related Disorders", "Sleep-Wake Disorders", "Anxiety Disorders", "Depressive Disorders"],
      "Impulse Control": ["Obsessive-Compulsive and Related Disorders", "Disruptive, Impulse-Control, and Conduct Disorders", "Personality Disorders", "Substance Related and Addictive Disorders", "Bipolar and Related Disorders"],
      "Restlessness": ["Anxiety Disorders", "Neurodevelopmental Disorders"],
      "Fatigue": ["Depressive Disorders"],
      "Repetitive Behaviors": ["Neurodevelopmental Disorders", "Obsessive-Compulsive and Related Disorders"],
      "Changes in Appetite": ["Depressive Disorders", "Feeding and Eating Disorders"],
      "Excessive Worry": ["Anxiety Disorders"],
      "Difficulty Concentrating": ["Depressive Disorders", "Neurodevelopmental Disorders"],
      "Burned Out": ["Depressive Disorders", "Self-Care / Wellness"],
      "Stressed": ["Anxiety Disorders", "Trauma and Stressor Related Disorders", "Self-Care / Wellness"],
      "Social Difficulties": ["Anxiety Disorders", "Neurodevelopmental Disorders"],
      "Hallucinations": ["Schizophrenia Spectrum and Other Psychotic Disorders"],
      "Delusions": ["Schizophrenia Spectrum and Other Psychotic Disorders"],
      "Persistent Sadness": ["Depressive Disorders"],
      "Intrusive Thoughts": ["Trauma and Stressor Related Disorders", "Obsessive-Compulsive and Related Disorders"],
      "Flashbacks": ["Trauma and Stressor Related Disorders"],
      "Nightmares": ["Trauma and Stressor Related Disorders"],
      "Body Image Distortion": ["Feeding and Eating Disorders"],
      "Binging (Food)": ["Feeding and Eating Disorders"],
      "Restricting (Food)": ["Feeding and Eating Disorders"],
      "Daytime Sleepiness": ["Sleep-Wake Disorders", "Depressive Disorders"],
      "Low Sex Drive": ["Depressive Disorders", "Sexual Dysfunctions"],
      "Erectile Dysfunction": ["Sexual Dysfunctions"],
      "Gender Discomfort": ["Gender Dysphoria"],
      "Aggressive Behavior": ["Disruptive, Impulse-Control, and Conduct Disorders", "Personality Disorders"],
      "Substance Cravings": ["Substance Related and Addictive Disorders"],
      "Lack of Self-Control": ["Disruptive, Impulse-Control, and Conduct Disorders", "Substance Related and Addictive Disorders"],
      "Memory Issues": ["NeuroCognitive Disorders"],
      "Confusion": ["NeuroCognitive Disorders"],
      "Communication Issues": ["Neurodevelopmental Disorders", "Relationship Issues", "Disruptive, Impulse-Control, and Conduct Disorders"],
      "Unstable Relationships": ["Relationship Issues", "Personality Disorders", "Disruptive, Impulse-Control, and Conduct Disorders"],
      "Argumentative Behavior": ["Relationship Issues", "Personality Disorders"]
    };
    
    // Get all required tags from all selected concerns
    const allRequiredTags = new Set<string>();
    concerns.forEach(concernKey => {
      if (symptomToTag[concernKey]) {
        symptomToTag[concernKey].forEach(tag => allRequiredTags.add(tag));
      }
    });
    console.log('All required tags from concerns:', Array.from(allRequiredTags));
    
    // Build base query
    const query: any = {
      role: 'THERAPIST',
      verifiedStatus: 'VERIFIED',
      adminApproved: true,
      blockedByAdmin: { $ne: true },
    };

    // Add state filter
    if (state) {
      query.therapyState = { $in: [state] };
    }

    // Add insurance filter - FIXED LOGIC
    if (insurance) {
      // Only include therapists who have this specific insurance AND have a non-null insuranceCompanies array
      query.insuranceCompanies = { 
        $exists: true, 
        $ne: null, 
        $in: [insurance] 
      };
    }

    // Add gender filter
    if (genderFilters.length > 0) {
      query.gender = { $in: genderFilters };
    }

    // Add ethnicity filter
    if (ethnicityFilters.length > 0) {
      // First, get ethnicity ObjectIds from the ethnicity names
      const ethnicityDocs = await Ethnicity.find({ 
        name: { $in: ethnicityFilters } 
      }).select('_id').lean();
      
      if (ethnicityDocs.length > 0) {
        const ethnicityIds = ethnicityDocs.map(doc => doc._id);
        query.ethnicityId = { $in: ethnicityIds };
      } else {
        // If no matching ethnicity documents found, return empty result
        console.log('No matching ethnicity documents found for filters:', ethnicityFilters);
        return res.status(200).json({
          status: 'success',
          data: [],
        });
      }
    }
    
    console.log('Query criteria:', JSON.stringify(query, null, 2));
    
    // Get all therapists first (don't sort by priority yet, we'll sort by concern relevance first)
    const therapists = await User.find(query).lean();
    
    console.log(`Found ${therapists.length} therapists matching criteria`);
    
    // Enhance each therapist with image, experienceTags, and next availability
    const enhancedTherapists = await Promise.all(therapists.map(async (t: any) => {
      let image = '';
      if (t.photoId) {
        const upload = await Upload.findById(t.photoId).lean();
        if (upload?.path) {
          image = upload.path;
          console.log(`Therapist ${t.firstname} ${t.lastname} image path:`, image);
        }
      }
      let experienceTags: string[] = [];
      if (Array.isArray(t.experiencedIn) && t.experiencedIn.length > 0) {
        const tags = await ExperienceTag.find({ _id: { $in: t.experiencedIn } }).lean();
        experienceTags = tags.map(tag => tag.experienceTag).sort();
      }

      // Resolve insurance companies
      let insuranceCompanies: string[] = [];
      if (Array.isArray(t.insuranceCompanies) && t.insuranceCompanies.length > 0) {
        const insurances = await InsuranceCompany.find({ _id: { $in: t.insuranceCompanies } }).lean();
        // Filter out any insurance companies that don't have a valid organizationName and sort alphabetically
        insuranceCompanies = insurances
          .map(insurance => insurance.organizationName)
          .filter(name => name && name.trim().length > 0)
          .sort(); // Sort alphabetically
        console.log(`Therapist ${t.firstname} ${t.lastname} insurance companies:`, insuranceCompanies);
      } else {
        console.log(`Therapist ${t.firstname} ${t.lastname} has no insurance companies assigned`);
      }

      // Resolve ethnicity
      let ethnicityName = '';
      if (t.ethnicityId) {
        const ethnicityDoc = await Ethnicity.findById(t.ethnicityId).lean();
        ethnicityName = ethnicityDoc?.name || '';
      }

      const originalDescription = t.description || '';
      const bio = htmlToPlainText(originalDescription);

      // Get therapist's appointments
      const appointments = await Appointment.find({
        therapistId: t._id,
        status: { $ne: 'CANCELLED' },
        start: { $gte: new Date() }
      }).sort({ start: 1 });

      // Normalize working hours to match frontend logic - keep day case and time format as-is
      const normalizedWorkingHours = Array.isArray(t.workingHours)
        ? t.workingHours.map((wh: any) => ({
            day: wh.day || '', // Keep original case (e.g., "Tuesday")
            startTime: wh.startTime || '', // Keep original format (e.g., "11:30 AM")
            endTime: wh.endTime || '', // Keep original format (e.g., "19:30 PM"),
          }))
        : [];
      // Calculate next availability using new async function
      const nextAvailability = await calculateNextAvailability(
        t._id.toString(),
        normalizedWorkingHours
      );
      
      // Check if therapist has experience matching any of the selected concerns
      let hasConcernMatch = false;
      let matchingConcerns: string[] = [];
      if (concerns.length > 0) {
        // Check if therapist has any tags that match the required tags from all concerns
        hasConcernMatch = experienceTags.some(tag => allRequiredTags.has(tag));
        
        // Also track which specific concerns this therapist matches
        matchingConcerns = concerns.filter(concernKey => {
          if (symptomToTag[concernKey]) {
            return symptomToTag[concernKey].some(tag => experienceTags.includes(tag));
          }
          return false;
        });
      }
      
      console.log('--- BACKEND LOG ---');
      console.log(`Therapist: ${t.firstname} ${t.lastname}`);
      console.log('  experienceTags:', experienceTags);
      console.log('  selected concerns:', concerns);
      console.log('  matching concerns:', matchingConcerns);
      console.log('  hasConcernMatch:', hasConcernMatch);
      console.log('  insuranceCompanies (resolved):', insuranceCompanies);
      console.log('  insuranceCompanies (raw IDs):', t.insuranceCompanies);
      console.log('  nextAvailability:', nextAvailability);

      return {
        _id: t._id,
        firstname: t.firstname,
        lastname: t.lastname,
        city: t.city,
        state: t.state,
        gender: t.gender,
        image,
        photoId: t.photoId,
        experienceTags,
        specialties: experienceTags,
        bio,
        nextAvailability,
        insuranceCompanies,
        hasConcernMatch,
        matchingConcerns,
        priorityNumber: t.priorityNumber || 0,
        ethnicityName,
      };
    }));
    
    // Sort therapists: first by concern match (if concerns are provided), then by priority score
    const sortedTherapists = enhancedTherapists.sort((a, b) => {
      if (concerns.length > 0) {
        // If one has concern match and the other doesn't, prioritize the one with match
        if (a.hasConcernMatch && !b.hasConcernMatch) return -1;
        if (!a.hasConcernMatch && b.hasConcernMatch) return 1;
        
        // If both have concern matches, prioritize the one with more matching concerns
        if (a.hasConcernMatch && b.hasConcernMatch) {
          const aMatchCount = a.matchingConcerns.length;
          const bMatchCount = b.matchingConcerns.length;
          if (aMatchCount !== bMatchCount) return bMatchCount - aMatchCount;
        }
        
        // If same concern match status, sort by priority
        return a.priorityNumber - b.priorityNumber;
      } else {
        // No concern filter, just sort by priority
        return a.priorityNumber - b.priorityNumber;
      }
    });
    
    // Remove the internal fields before sending response
    const finalTherapists = sortedTherapists.map(t => {
      const { hasConcernMatch, matchingConcerns, priorityNumber, ...therapist } = t;
      return therapist;
    });
    
    console.log('Query completed. Found therapists:', finalTherapists.length);
    res.status(200).json({
      status: 'success',
      results: finalTherapists.length,
      data: { therapists: finalTherapists },
    });
  } catch (error) {
    console.error('Error in getAllTherapists:', error);
    next(error);
  }
};

export const getTherapistById = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    let therapist = await User.findOne({
      _id: req.params.id,
      role: 'THERAPIST',
      verifiedStatus: 'VERIFIED',
      adminApproved: true,
    }).lean();
    if (!therapist) {
      throw new AppError('Therapist not found', 404);
    }
    // Resolve image path
    let image = '';
    if (therapist.photoId) {
      const upload = await Upload.findById(therapist.photoId).lean();
      image = upload?.path || '';
    }
    // Resolve experience tags
    let experienceTags: string[] = [];
    if (Array.isArray(therapist.experiencedIn) && therapist.experiencedIn.length > 0) {
      const tags = await ExperienceTag.find({ _id: { $in: therapist.experiencedIn } }).lean();
      experienceTags = tags.map(tag => tag.experienceTag).sort();
    }

    // Resolve insurance companies
    let insuranceCompanies: string[] = [];
    if (Array.isArray(therapist.insuranceCompanies) && therapist.insuranceCompanies.length > 0) {
      // Assuming you have an InsuranceCompany model - adjust the import and model name as needed
      const insurances = await InsuranceCompany.find({ _id: { $in: therapist.insuranceCompanies } }).lean();
      // Filter out any insurance companies that don't have a valid organizationName and sort alphabetically
      insuranceCompanies = insurances
        .map(insurance => insurance.organizationName)
        .filter(name => name && name.trim().length > 0)
        .sort(); // Sort alphabetically
    }

    // Resolve ethnicity
    let ethnicityName = '';
    if (therapist.ethnicityId) {
      const ethnicityDoc = await Ethnicity.findById(therapist.ethnicityId).lean();
      ethnicityName = ethnicityDoc?.name || '';
    }

    therapist = {
      _id: therapist._id,
      firstname: therapist.firstname,
      lastname: therapist.lastname,
      city: therapist.city,
      state: therapist.state,
      gender: therapist.gender,
      image: image,
      photoId: therapist.photoId,
      experienceTags: experienceTags,
      specialties: experienceTags,
      bio: htmlToPlainText(therapist.description || ''),
      insuranceCompanies,
      ethnicityName,
    } as any;
    res.status(200).json({
      status: 'success',
      data: { therapist },
    });
  } catch (error) {
    next(error);
  }
};

export const updateTherapistAvailability = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    // Ensure the user is a therapist
    if (req.user.role !== 'THERAPIST') {
      throw new AppError('Only therapists can update availability', 403);
    }

    const updatedTherapist = await User.findByIdAndUpdate(
      req.user._id,
      {
        $set: {
          workingHours: req.body.workingHours,
          blockedDates: req.body.blockedDates,
        },
      },
      { new: true, runValidators: true }
    ).select('-password');

    res.status(200).json({
      status: 'success',
      data: { therapist: updatedTherapist },
    });
  } catch (error) {
    next(error);
  }
}; 