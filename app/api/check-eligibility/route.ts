import { NextResponse } from 'next/server';
import { MongoClient, ObjectId } from 'mongodb';
import { XMLParser } from 'fast-xml-parser';

const CLAIM_MD_URL = 'https://svc.claim.md/services/eligdata/';
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017';
const DB_NAME = process.env.MONGODB_DB || 'lavni';
const ACCOUNT_KEY = process.env.CLAIM_MD_ACCOUNT_KEY;
console.log('Loaded CLAIM_MD_ACCOUNT_KEY:', ACCOUNT_KEY ? ACCOUNT_KEY.slice(0, 6) + '...' : 'undefined');
console.log('NODE_ENV:', process.env.NODE_ENV);
console.log('process.cwd():', process.cwd());
console.log('__dirname:', typeof __dirname !== 'undefined' ? __dirname : 'undefined');
console.log('All env keys:', Object.keys(process.env));

const client = new MongoClient(MONGODB_URI);

function toFormUrlEncoded(obj: Record<string, any>) {
  return Object.entries(obj)
    .map(([k, v]) => `${encodeURIComponent(k)}=${encodeURIComponent(v)}`)
    .join('&');
}

function formatDateYYYYMMDD(date: string | Date | undefined) {
  if (!date) return '';
  const d = new Date(date);
  return d.toISOString().slice(0, 10).replace(/-/g, '');
}

export async function POST(req: Request) {
  try {
    const body = await req.json();
    console.log('Received request body:', body);

    await client.connect();
    const db = client.db(DB_NAME);
    const insuranceCompanies = db.collection('insurancecompanies');
    const eligibilityResults = db.collection('eligibilityResults');

    // Use the tradingPartnerServiceId directly from the request body
    const tradingPartnerServiceId = body.insuranceCompanyId;
    console.log('Using tradingPartnerServiceId:', tradingPartnerServiceId);

    // Build the payload for Claim.MD
    const payload = {
      'ins_name_f': body.firstName,
      'ins_name_l': body.lastName,
      'payerid': tradingPartnerServiceId,
      'ins_dob': body.dateOfBirth,
      'pat_rel': body.relationshipCode,
      'fdos': body.serviceDate || formatDateYYYYMMDD(new Date()),
      'ins_sex': body.gender,
      'prov_npi': process.env.PROVIDER_NPI,
      'prov_taxid': process.env.PROVIDER_TAX_ID,
      'ins_number': body.memberId,
      'AccountKey': ACCOUNT_KEY
    };

    console.log('Sending payload to Claim.MD:', payload);

    // Send request to Claim.MD
    const response = await fetch(CLAIM_MD_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      },
      body: toFormUrlEncoded(payload)
    });

    const result = await response.text();
    const parser = new XMLParser({
      ignoreAttributes: false,
      attributeNamePrefix: '',
      isArray: (name) => name === 'benefit',
    });
    const parsedResult = parser.parse(result);

    // Clean up the benefit array if it exists
    if (
      parsedResult?.result?.elig?.benefit &&
      Array.isArray(parsedResult.result.elig.benefit)
    ) {
      parsedResult.result.elig.benefit = parsedResult.result.elig.benefit.filter(
        (b: any) => typeof b === 'object' && b !== null && Object.keys(b).length > 0
      );
    }

    // Add eligibilityStatus for frontend compatibility
    const hasActiveCoverage = parsedResult?.result?.elig?.benefit?.some(
      (b: any) => b.benefit_coverage_description === 'Active Coverage'
    );
    parsedResult.eligibilityStatus = hasActiveCoverage ? 'ACTIVE' : 'NOT_FOUND';

    // --- Copay Logic ---
    let copayAmount = null;
    let copayMessage = null;
    // Look up insurance company by tradingPartnerServiceId
    const insuranceCompany = await insuranceCompanies.findOne({ tradingPartnerServiceId: tradingPartnerServiceId });
    if (insuranceCompany && insuranceCompany.isMedicaid) {
      copayAmount = 0;
    } else if (parsedResult?.result?.elig?.benefit && Array.isArray(parsedResult.result.elig.benefit)) {
      // Try to find Mental Health copay
      let copayBenefit = parsedResult.result.elig.benefit.find(
        (b: any) =>
          b.benefit_coverage_description === 'Co-Payment' &&
          b.benefit_description === 'Mental Health' &&
          b.benefit_amount !== undefined
      );
      // If not found, try Professional (Physician) Visit - Office
      if (!copayBenefit) {
        copayBenefit = parsedResult.result.elig.benefit.find(
          (b: any) =>
            b.benefit_coverage_description === 'Co-Payment' &&
            b.benefit_description === 'Professional (Physician) Visit - Office' &&
            b.benefit_amount !== undefined
        );
      }
      if (copayBenefit && !isNaN(Number(copayBenefit.benefit_amount))) {
        copayAmount = Number(copayBenefit.benefit_amount);
      } else {
        copayAmount = 0;
      }
    }
    // If not Medicaid and copayAmount is 0, set copayMessage
    if (insuranceCompany && !insuranceCompany.isMedicaid && copayAmount === 0) {
      copayMessage = 'We could not determine your copayment at this time. A member of our team will reach out to you shortly.';
    }
    parsedResult.copayAmount = copayAmount;
    if (copayMessage) parsedResult.copayMessage = copayMessage;

    console.log('Parsed and cleaned Claim.MD response:', parsedResult);

    // Save the result to MongoDB
    await eligibilityResults.insertOne({
      request: payload,
      response: parsedResult,
      timestamp: new Date()
    });

    return NextResponse.json(parsedResult);
  } catch (error) {
    console.error('Error checking eligibility:', error);
    return NextResponse.json({ error: 'Failed to check eligibility' }, { status: 500 });
  } finally {
    await client.close();
  }
} 