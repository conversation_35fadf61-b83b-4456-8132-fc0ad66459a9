import mongoose, { Document, Schema, Types, model } from 'mongoose';
import bcrypt from 'bcryptjs';

export interface IUser extends Document {
  _id: Types.ObjectId;
  role: 'CLIENT' | 'THERAPIST';
  email: string;
  password: string;
  firstname: string;
  lastname: string;
  verifiedStatus: 'VERIFIED' | 'PENDING' | 'UNVERIFIED';
  adminApproved: boolean;
  photoId?: Types.ObjectId;
  coverPhotoId?: Types.ObjectId;
  photoThumbnailId?: Types.ObjectId;
  city?: string;
  state?: string;
  dateOfBirth: Date;
  gender?: string;
  primaryPhone?: string;
  description?: string;
  dislikedTherapists?: Types.ObjectId[];
  blockedUser?: Types.ObjectId[];
  friendRequests?: Types.ObjectId[];
  guideComplete?: boolean;
  googleId?: string;
  medium?: string;
  paymentDetails?: any[];
  experiencedIn?: Types.ObjectId[];
  priorityNumber?: number;
  insuranceCompanies?: Types.ObjectId[];
  insuranceId?: Types.ObjectId;
  secondaryInsuranceId?: Types.ObjectId;
  ethnicityId?: Types.ObjectId;
  claimEligibilityDetails?: any;
  workingHours?: {
    _id?: Types.ObjectId;
    day: string;
    startTime: string;
    endTime: string;
  }[];
  blockedDates?: {
    title?: string;
    start: Date | string;
    end: Date | string;
    display?: string;
    className?: string;
    extendedProps?: any;
  }[];
  clientType: 'adult' | 'minor';
  parent?: {
    parentFirstName?: string;
    parentLastName?: string;
    parentEmail?: string;
    parentPhone?: string;
  };
  comparePassword(candidatePassword: string): Promise<boolean>;
}

// Parent subdocument for minors
const parentSchema = new Schema({
  parentFirstName: { type: String },
  parentLastName: { type: String },
  parentEmail: { type: String },
  parentPhone: { type: String },
}, { _id: false });

const userSchema = new Schema<IUser>(
  {
    role: {
      type: String,
      enum: ['CLIENT', 'THERAPIST'],
      required: true,
    },
    email: {
      type: String,
      required: true,
      unique: true,
      lowercase: true,
      trim: true,
    },
    password: {
      type: String,
      required: true,
      minlength: 8,
    },
    firstname: {
      type: String,
      required: true,
      trim: true,
    },
    lastname: {
      type: String,
      required: true,
      trim: true,
    },
    verifiedStatus: {
      type: String,
      enum: ['VERIFIED', 'PENDING', 'UNVERIFIED'],
      default: 'UNVERIFIED',
    },
    adminApproved: {
      type: Boolean,
      default: false,
    },
    photoId: {
      type: Schema.Types.ObjectId,
      ref: 'Photo',
    },
    coverPhotoId: {
      type: Schema.Types.ObjectId,
      ref: 'Photo',
    },
    photoThumbnailId: {
      type: Schema.Types.ObjectId,
      ref: 'Upload',
    },
    city: String,
    state: String,
    dateOfBirth: {
      type: Date,
      required: true,
    },
    gender: String,
    primaryPhone: String,
    description: String,
    dislikedTherapists: [{
      type: Schema.Types.ObjectId,
      ref: 'User',
    }],
    blockedUser: [{
      type: Schema.Types.ObjectId,
      ref: 'User',
    }],
    friendRequests: [{
      type: Schema.Types.ObjectId,
      ref: 'User',
    }],
    guideComplete: {
      type: Boolean,
      default: false,
    },
    googleId: String,
    medium: String,
    paymentDetails: [{
      type: Schema.Types.Mixed,
    }],
    experiencedIn: [{
      type: Schema.Types.ObjectId,
      ref: 'ExperienceTag',
    }],
    priorityNumber: {
      type: Number,
      default: 0,
    },
    insuranceCompanies: [{
      type: Schema.Types.ObjectId,
      ref: 'InsuranceCompany',
    }],
    insuranceId: {
      type: Schema.Types.ObjectId,
      ref: 'Insurance',
    },
    secondaryInsuranceId: {
      type: Schema.Types.ObjectId,
      ref: 'Insurance',
    },
    ethnicityId: {
      type: Schema.Types.ObjectId,
      ref: 'Ethnicity',
    },
    claimEligibilityDetails: {
      type: Schema.Types.Mixed,
    },
    workingHours: [{
      day: String,
      startTime: String,
      endTime: String,
    }],
    blockedDates: [{
      title: String,
      start: Date,
      end: Date,
      display: String,
      className: String,
      extendedProps: Schema.Types.Mixed,
    }],
    clientType: {
      type: String,
      enum: ['adult', 'minor'],
      required: true,
    },
    parent: {
      type: parentSchema,
      required: false,
    },
  },
  {
    timestamps: true,
  }
);

// Hash password before saving
userSchema.pre('save', async function(next) {
  if (!this.isModified('password')) return next();
  
  try {
    const salt = await bcrypt.genSalt(10);
    this.password = await bcrypt.hash(this.password, salt);
    next();
  } catch (error: any) {
    next(error);
  }
});

// Compare password method
userSchema.methods.comparePassword = async function(candidatePassword: string): Promise<boolean> {
  return bcrypt.compare(candidatePassword, this.password);
};

export const User = mongoose.model<IUser>('User', userSchema); 