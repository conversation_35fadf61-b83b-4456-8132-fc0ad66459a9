import { Router } from 'express';
import { register, login, verifyEmail, googleAuth } from '../controllers/auth.controller';
import { validateRegistration, validateLogin } from '../middleware/validation.middleware';

const router = Router();

router.post('/register', validateRegistration, register);
router.post('/login', validateLogin, login);
router.post('/verify-email', verifyEmail);
router.post('/google', googleAuth);

export default router; 