import mongoose, { Document, Schema, Types } from 'mongoose';

export interface IExperienceTag extends Document {
  experienceTag: string;
  experienceMainTags: Types.ObjectId[];
  createdAt: Date;
  updatedAt: Date;
}

const experienceTagSchema = new Schema<IExperienceTag>(
  {
    experienceTag: String,
    experienceMainTags: [{ type: Schema.Types.ObjectId, ref: 'ExperienceMainTag' }],
  },
  { timestamps: true }
);

export const ExperienceTag = mongoose.model<IExperienceTag>('ExperienceTag', experienceTagSchema); 