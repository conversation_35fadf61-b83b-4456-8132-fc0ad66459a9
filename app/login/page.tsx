"use client"

import { useState, useEffect, Suspense } from "react"
import { useRouter, useSearchParams } from "next/navigation"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { GoogleOAuthButton } from "@/components/google-oauth-button"
import { toast } from "sonner"
import { Eye, EyeOff } from "lucide-react"

function LoginPageContent() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const [email, setEmail] = useState("")
  const [password, setPassword] = useState("")
  const [showPassword, setShowPassword] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)

  useEffect(() => {
    const type = searchParams.get("type")
    if (type === "google") {
      toast.info("Please sign in with Google to continue")
    }
  }, [searchParams])

  const handleEmailLogin = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!email || !password) {
      toast.error("Please enter both email and password")
      return
    }

    setIsSubmitting(true)
    
    try {
      const response = await fetch("https://api.lavnihealth.com/api/public/login", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          email,
          password,
          medium: "EMAIL"
        }),
      })

      const data = await response.json()
      
      if (response.ok && data.success) {
        // Store auth token
        localStorage.setItem("authToken", data.data.authToken || data.data)
        localStorage.setItem("currentUser", JSON.stringify(data.data))
        
        toast.success("Login successful!")
        router.push("/dashboard")
      } else {
        toast.error(data.message || "Login failed. Please check your credentials.")
      }
    } catch (error) {
      console.error("Login error:", error)
      toast.error("Login failed. Please try again.")
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleGoogleSuccess = async (googleUserData: any) => {
    setIsSubmitting(true)
    
    try {
      const response = await fetch("https://api.lavnihealth.com/api/public/loginWithGoogle", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          idToken: {
            credential: googleUserData.idToken,
            clientId: googleUserData.clientId,
          }
        }),
      })

      const data = await response.json()
      
      if (response.ok && data.success) {
        // Store auth token
        localStorage.setItem("authToken", data.data)
        localStorage.setItem("currentUser", JSON.stringify(data))
        
        toast.success("Login successful!")
        router.push("/dashboard")
      } else {
        toast.error(data.message || "Google login failed. Please try again.")
      }
    } catch (error) {
      console.error("Google login error:", error)
      toast.error("Google login failed. Please try again.")
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleGoogleError = (error: any) => {
    console.error("Google OAuth error:", error)
    toast.error("Google sign-in failed. Please try again.")
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div>
          <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
            Sign in to your account
          </h2>
          <p className="mt-2 text-center text-sm text-gray-600">
            Welcome back to Lavni
          </p>
        </div>
        
        <div className="mt-8 space-y-6">
          {/* Google OAuth Button */}
          <GoogleOAuthButton
            onSuccess={handleGoogleSuccess}
            onError={handleGoogleError}
            clientType="adult"
            disabled={isSubmitting}
          />
          
          <div className="relative">
            <div className="absolute inset-0 flex items-center">
              <div className="w-full border-t border-gray-300" />
            </div>
            <div className="relative flex justify-center text-sm">
              <span className="px-2 bg-gray-50 text-gray-500">Or continue with email</span>
            </div>
          </div>

          {/* Email/Password Form */}
          <form className="space-y-6" onSubmit={handleEmailLogin}>
            <div>
              <Label htmlFor="email">Email address</Label>
              <Input
                id="email"
                name="email"
                type="email"
                autoComplete="email"
                required
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="mt-1"
                disabled={isSubmitting}
              />
            </div>
            
            <div>
              <Label htmlFor="password">Password</Label>
              <div className="relative mt-1">
                <Input
                  id="password"
                  name="password"
                  type={showPassword ? "text" : "password"}
                  autoComplete="current-password"
                  required
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  disabled={isSubmitting}
                />
                <button
                  type="button"
                  className="absolute inset-y-0 right-0 pr-3 flex items-center"
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {showPassword ? (
                    <EyeOff className="h-4 w-4 text-gray-400" />
                  ) : (
                    <Eye className="h-4 w-4 text-gray-400" />
                  )}
                </button>
              </div>
            </div>

            <div>
              <Button
                type="submit"
                className="w-full bg-[#F7903D] hover:bg-[#e67f2d] text-white"
                disabled={isSubmitting}
              >
                {isSubmitting ? "Signing in..." : "Sign in"}
              </Button>
            </div>
          </form>

          <div className="text-center">
            <Button
              variant="link"
              onClick={() => router.push("/")}
              className="text-[#F7903D] hover:text-[#e67f2d]"
            >
              Don't have an account? Sign up
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}

export default function LoginPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#F7903D] mx-auto"></div>
          <p className="mt-2 text-gray-600">Loading...</p>
        </div>
      </div>
    }>
      <LoginPageContent />
    </Suspense>
  )
} 