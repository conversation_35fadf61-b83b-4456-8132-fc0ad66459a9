"use client"

import type { FC } from 'react'
import { useRouter } from "next/navigation"
import { useState, useEffect } from "react"
import { MobileStepTracker } from "@/components/mobile-step-tracker"
import { TherapistCardMobile } from "@/components/therapist-card-mobile"
import { TherapistProfileModal } from "@/components/therapist-profile-modal"
import { TherapistFilterModal } from "@/components/therapist-filter-modal"
import { Button } from "@/components/ui/button"
import { ChevronLeft, Phone, Filter } from "lucide-react"
import { DesktopLayout } from "@/components/desktop-layout"
import { MobileHeader } from "@/components/mobile-header"
import { therapistApi, Therapist } from "@/lib/api"
import { SpecialtyBadge } from "@/components/ui/specialty-badge"

interface FilterOptions {
  gender: string[]
  ethnicity: string[]
}

// Helper to format nextAvailability
function formatNextAvailability(availabilityString: string): string {
  // Backend now returns formatted strings like "Tuesday, June 4th at 11:30 AM"
  // Just return it as-is since it's already properly formatted
  return availabilityString || '';
}

const TherapistsPage: FC = () => {
  const router = useRouter()
  const [selectedTherapist, setSelectedTherapist] = useState<Therapist | null>(null)
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [isFilterModalOpen, setIsFilterModalOpen] = useState(false)
  const [onboardingData, setOnboardingData] = useState<any>(null)
  const [filters, setFilters] = useState<FilterOptions>({
    gender: [],
    ethnicity: [],
  })
  const [therapists, setTherapists] = useState<Therapist[]>([])
  const [filteredTherapists, setFilteredTherapists] = useState<Therapist[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [activeFilterCount, setActiveFilterCount] = useState(0)

  useEffect(() => {
    // Retrieve data from localStorage
    const storedData = localStorage.getItem("onboardingData")
    if (storedData) {
      setOnboardingData(JSON.parse(storedData))
    }

    // Fetch therapists from API
    const fetchTherapists = async () => {
      try {
        setIsLoading(true)
        const storedData = localStorage.getItem("onboardingData")
        const onboardingData = storedData ? JSON.parse(storedData) : null
        const state = onboardingData?.state
        const concerns = onboardingData?.therapyConcern || [] // Now an array
        const insurance = onboardingData?.insuranceObject?._id // Use the insurance object ID if available

        console.log('Fetching therapists with filters:', { 
          state, 
          concerns, 
          insurance, 
          genderFilters: filters.gender, 
          ethnicityFilters: filters.ethnicity 
        });
        
        const data = await therapistApi.getAllTherapists(
          state, 
          insurance, 
          filters.gender, 
          filters.ethnicity
        )
        
        // Map backend data to frontend Therapist type
        const mapped = data.map((t: any) => ({
          ...t,
          name: `${t.firstname || ''} ${t.lastname || ''}`.trim(),
          image: t.photoId 
            ? `https://api.lavnihealth.com/api/public/files/${t.photoId}`
            : t.image || '/placeholder.svg',
          title: t.title || '',
          specialties: t.specialties || [],
          licensedStates: t.licensedStates || [],
          nextAvailability: t.nextAvailability || '',
          bio: t.bio || '',
          videoUrl: t.videoUrl || '',
          gender: t.gender || '',
          experience: t.experience || '',
          ethnicity: t.ethnicityName || '',
          adminApproved: t.adminApproved || false,
        }))
        console.log('Mapped therapists:', {mapped})
        setTherapists(mapped)
        setFilteredTherapists(mapped)
        
        // Count active filters
        const count = filters.gender.length + filters.ethnicity.length
        setActiveFilterCount(count)
      } catch (err) {
        setError('Failed to load therapists. Please try again later.')
        console.error('Error fetching therapists:', err)
      } finally {
        setIsLoading(false)
      }
    }

    fetchTherapists()
  }, [filters]) // Re-fetch when filters change

  const handleTherapistSelect = (therapist: Therapist) => {
    // Store selected therapist in localStorage
    localStorage.setItem("selectedTherapist", JSON.stringify(therapist))
    router.push("/calendar")
  }

  const handleSkipTherapistSelection = () => {
    // Clear any previously selected therapist and proceed to registration
    localStorage.removeItem("selectedTherapist")
    // Store onboarding data without therapist selection
    const storedData = localStorage.getItem("onboardingData")
    if (storedData) {
      const onboardingData = JSON.parse(storedData)
      onboardingData.skippedTherapistSelection = true
      localStorage.setItem("onboardingData", JSON.stringify(onboardingData))
    }
    // Navigate to who-is-this-for page to determine adult vs minor registration
    router.push("/who-is-this-for")
  }

  const openTherapistProfile = (therapist: Therapist) => {
    setSelectedTherapist(therapist)
    setIsModalOpen(true)
  }

  const handleApplyFilters = (newFilters: FilterOptions) => {
    setFilters(newFilters)
  }

  // Desktop therapist card component
  const DesktopTherapistCard: FC<{ therapist: Therapist }> = ({ therapist }) => {
    const SPECIALTIES_PER_PAGE = 4;
    const [specialtyIndex, setSpecialtyIndex] = useState(0);
    const [imageError, setImageError] = useState(false);

    useEffect(() => {
      if (!therapist.specialties || therapist.specialties.length <= SPECIALTIES_PER_PAGE) return;
      const interval = setInterval(() => {
        setSpecialtyIndex((prev: number) =>
          prev + SPECIALTIES_PER_PAGE >= therapist.specialties.length ? 0 : prev + SPECIALTIES_PER_PAGE
        );
      }, 5000); // 5 seconds per rotation
      return () => clearInterval(interval);
    }, [therapist.specialties]);

    const visibleSpecialties = therapist.specialties.slice(
      specialtyIndex,
      specialtyIndex + SPECIALTIES_PER_PAGE
    );

    return (
      <div className="border border-gray-200 rounded-lg overflow-hidden bg-white shadow-sm hover:shadow-md transition-shadow w-full">
        <div className="flex flex-row items-stretch gap-6 p-6">
          <div className="relative w-36 h-36 rounded-full overflow-hidden border-2 border-[#FEC07E] cursor-pointer flex-shrink-0" onClick={() => openTherapistProfile(therapist)}>
            <img
              src={imageError ? "/placeholder.svg" : therapist.image || "/placeholder.svg"}
              alt={therapist.name}
              className="object-cover w-full h-full"
              onError={() => setImageError(true)}
            />
          </div>
          <div className="flex flex-col flex-1 justify-between">
            <div>
              <h3 className="text-xl font-semibold text-gray-900">{therapist.name}</h3>
              <p className="text-gray-600">{therapist.title}</p>
              {therapist.nextAvailability ? (
                <p className="text-sm text-[#0f3460] font-medium mt-1">
                  Next availability: {formatNextAvailability(therapist.nextAvailability)}
                </p>
              ) : (
                <p className="text-sm text-gray-400 font-medium mt-1">No availability</p>
              )}
              <div className="flex flex-wrap gap-2 mt-3">
                {visibleSpecialties.map((specialty: string, index: number) => (
                  <SpecialtyBadge 
                    key={index} 
                    text={specialty} 
                  />
                ))}
              </div>
            </div>
            <p className="text-gray-700 text-sm mt-4 line-clamp-2 cursor-pointer" onClick={() => openTherapistProfile(therapist)}>{therapist.bio}</p>
          </div>
          <div className="flex flex-col gap-2 justify-center ml-4">
            <Button
              onClick={() => openTherapistProfile(therapist)}
              variant="outline"
              className="border-[#64748B] text-[#64748B]"
            >
              View Profile
            </Button>
            <Button
              onClick={() => handleTherapistSelect(therapist)}
              className="bg-[#F7903D] hover:bg-[#e67f2d] text-white"
            >
              Schedule
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <>
      {/* Desktop Layout */}
      <DesktopLayout title="Choose a Therapist" subtitle="Based on your needs, here are therapists who can help.">
        <div className="bg-white px-2 py-8 rounded-lg shadow-sm max-w-full">
          <div className="flex justify-between items-center mb-6">
            <MobileStepTracker currentStep={2} totalSteps={7} />
            <Button
              variant="outline"
              onClick={() => setIsFilterModalOpen(true)}
              className="flex items-center gap-1 h-9"
            >
              <Filter size={16} />
              Filter
              {activeFilterCount > 0 && (
                <span className="ml-1 bg-[#F7903D] text-white rounded-full w-5 h-5 flex items-center justify-center text-xs">
                  {activeFilterCount}
                </span>
              )}
            </Button>
          </div>

          {isLoading ? (
            <div className="flex flex-col items-center justify-center min-h-[60vh]">
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#F7903D]"></div>
              <p className="mt-4 text-gray-600">Loading therapists...</p>
            </div>
          ) : error ? (
            <div className="flex flex-col items-center justify-center min-h-[60vh]">
              <p className="text-red-500 text-center">{error}</p>
              <Button
                onClick={() => window.location.reload()}
                variant="outline"
                className="mt-4 border-[#64748B] text-[#64748B]"
              >
                Try Again
              </Button>
            </div>
          ) : (
            <>
              {/* Mobile View */}
              <div className="lg:hidden">
                <div className="flex items-center justify-between mb-6">
                  <Button variant="ghost" onClick={() => router.back()} className="p-0 h-auto hover:bg-transparent">
                    <ChevronLeft className="h-6 w-6" />
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => setIsFilterModalOpen(true)}
                    className="border-[#64748B] text-[#64748B]"
                  >
                    <Filter className="h-4 w-4 mr-2" />
                    Filter {activeFilterCount > 0 && `(${activeFilterCount})`}
                  </Button>
                </div>

                <div className="space-y-4">
                  {filteredTherapists.map((therapist) => (
                    <div key={therapist._id} className="mb-4">
                      <TherapistCardMobile therapist={therapist} onSelect={handleTherapistSelect} onProfileOpen={openTherapistProfile} />
                    </div>
                  ))}
                </div>
              </div>

              {/* Desktop View */}
              <div className="hidden lg:block">
                <div className="flex items-center justify-between mb-6">
                  <div className="flex items-center gap-4">
                    <Button variant="ghost" onClick={() => router.back()} className="p-0 h-auto hover:bg-transparent">
                      <ChevronLeft className="h-6 w-6" />
                    </Button>
                    <h1 className="text-2xl font-semibold">Choose Your Therapist</h1>
                  </div>
                  <Button
                    onClick={handleSkipTherapistSelection}
                    variant="outline"
                    className="border-[#F7903D] text-[#F7903D] hover:bg-[#F7903D] hover:text-white"
                  >
                    Skip - I'll Choose a Therapist Later
                  </Button>
                </div>

                <div className="space-y-4 w-full">
                  {filteredTherapists.map((therapist) => (
                    <div key={therapist._id} className="mb-4">
                      <DesktopTherapistCard therapist={therapist} />
                    </div>
                  ))}
                </div>
              </div>
            </>
          )}

          <div className="mt-8 pt-6 border-t border-gray-200">
            <div className="flex items-center justify-center gap-2 mb-4">
              <Phone size={16} className="text-[#F7903D]" />
              <span className="text-gray-700">Need help? Call us at</span>
              <a href="tel:9808907995" className="font-bold text-[#F7903D]">
                ************
              </a>
            </div>
            <div className="flex items-center justify-center gap-4">
              <Button onClick={() => router.back()} variant="ghost" className="text-gray-600">
                <ChevronLeft size={16} className="mr-1" />
                Back
              </Button>
              <span className="text-gray-400">|</span>
              <Button
                onClick={handleSkipTherapistSelection}
                variant="outline"
                className="border-[#F7903D] text-[#F7903D] hover:bg-[#F7903D] hover:text-white"
              >
                Skip - I'll Choose a Therapist Later
              </Button>
            </div>
          </div>
        </div>
      </DesktopLayout>

      {/* Mobile Layout */}
      <main className="flex min-h-screen flex-col bg-white md:hidden">
        <MobileHeader showBackButton title="Choose a Therapist" />
        <div className="flex-1 px-4 py-6 pb-48">
          <div className="mb-4 flex items-center justify-between">
            <p className="text-gray-600">Based on your needs, here are therapists who can help.</p>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setIsFilterModalOpen(true)}
              className="flex items-center gap-1 h-9"
            >
              <Filter size={16} />
              Filter
              {activeFilterCount > 0 && (
                <span className="ml-1 bg-[#F7903D] text-white rounded-full w-5 h-5 flex items-center justify-center text-xs">
                  {activeFilterCount}
                </span>
              )}
            </Button>
          </div>

          <MobileStepTracker currentStep={2} totalSteps={7} />

          {isLoading ? (
            <div className="flex flex-col items-center justify-center min-h-[60vh]">
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#F7903D]"></div>
              <p className="mt-4 text-gray-600">Loading therapists...</p>
            </div>
          ) : error ? (
            <div className="flex flex-col items-center justify-center min-h-[60vh]">
              <p className="text-red-500 text-center">{error}</p>
              <Button
                onClick={() => window.location.reload()}
                variant="outline"
                className="mt-4 border-[#64748B] text-[#64748B]"
              >
                Try Again
              </Button>
            </div>
          ) : filteredTherapists.length === 0 ? (
            <div className="mt-8 text-center p-6 bg-gray-50 rounded-lg">
              <p className="text-gray-600 mb-2">No therapists match your filters</p>
              <Button
                variant="link"
                onClick={() => setIsFilterModalOpen(true)}
                className="text-[#F7903D] hover:text-[#e67f2d]"
              >
                Adjust filters
              </Button>
            </div>
          ) : (
            <div className="space-y-4">
              {filteredTherapists.map((therapist) => (
                <div key={therapist._id} className="mb-4">
                  <TherapistCardMobile therapist={therapist} onSelect={handleTherapistSelect} onProfileOpen={openTherapistProfile} />
                </div>
              ))}
            </div>
          )}
        </div>

        <div className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 p-4 z-50">
          <div className="flex flex-col gap-3 max-w-md mx-auto">
            <Button
              onClick={handleSkipTherapistSelection}
              variant="outline"
              className="flex items-center justify-center h-12 text-base font-medium border-[#F7903D] text-[#F7903D] hover:bg-[#F7903D] hover:text-white"
            >
              Skip - I'll Choose a Therapist Later
            </Button>
            
            <a
              href="tel:9808907995"
              className="flex flex-col items-center justify-center h-14 text-base font-medium bg-[#F7903D] hover:bg-[#e67f2d] text-white rounded-md"
            >
              <div className="flex items-center">
                <Phone size={16} className="mr-2" />
                Call us to help schedule your appointment
              </div>
              <span className="font-bold text-lg">************</span>
            </a>

            <Button
              onClick={() => router.back()}
              variant="outline"
              size="sm"
              className="flex items-center justify-center text-sm text-gray-600 h-10"
            >
              <ChevronLeft size={16} className="mr-1" />
              Back
            </Button>
          </div>
        </div>
      </main>

      {/* Modals */}
      {selectedTherapist && (
        <TherapistProfileModal
          therapist={selectedTherapist}
          isOpen={isModalOpen}
          onClose={() => setIsModalOpen(false)}
          onScheduleClick={() => {
            setIsModalOpen(false)
            handleTherapistSelect(selectedTherapist)
          }}
        />
      )}

      <TherapistFilterModal
        isOpen={isFilterModalOpen}
        onClose={() => setIsFilterModalOpen(false)}
        filters={filters}
        onFiltersChange={setFilters}
      />
    </>
  )
}

export default TherapistsPage
