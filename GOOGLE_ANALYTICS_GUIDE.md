# 📊 Hướng dẫn Google Analytics cho dự án Lavni

## 🚀 Cài đặt và Cấu hình

### Bước 1: Tạo tà<PERSON> Google Analytics
1. <PERSON><PERSON><PERSON> cập [Google Analytics](https://analytics.google.com/)
2. <PERSON><PERSON><PERSON> nhập bằng tài khoản Google
3. Tạo Account mới
4. Tạo Property cho website của bạn
5. Chọn platform là "Web"
6. Nhập thông tin website (URL, tên website, etc.)
7. Lấy **Measurement ID** (dạng `G-XXXXXXXXXX`)

### Bước 2: Cấu hình Environment Variable
Tạo file `.env.local` và thêm:
```bash
NEXT_PUBLIC_GA_ID=G-XXXXXXXXXX
```
*Thay `G-XXXXXXXXXX` bằng Measurement ID thực tế từ Google Analytics*

### Bước 3: Kiểm tra cài đặt
1. Chạy `npm run dev` hoặc `pnpm dev`
2. Mở website trong browser
3. Vào Google Analytics > Reports > Realtime để xem traffic

## 📈 Cách sử dụng

### 1. Tự động tracking page views
Page views sẽ được track tự động khi user điều hướng giữa các trang.

### 2. Track custom events
Sử dụng hook `useAnalytics`:

```tsx
import { useAnalytics } from '@/hooks/useAnalytics';

function MyComponent() {
  const { trackButtonClick, trackFormSubmit, trackUserAction } = useAnalytics();

  const handleButtonClick = () => {
    trackButtonClick('Register Button', 'Homepage');
    // Logic xử lý button
  };

  const handleFormSubmit = (success: boolean) => {
    trackFormSubmit('Registration Form', success);
  };

  return (
    <div>
      <button onClick={handleButtonClick}>
        Đăng ký
      </button>
    </div>
  );
}
```

### 3. Track custom events trực tiếp
```tsx
import { useAnalytics } from '@/hooks/useAnalytics';

function MyComponent() {
  const { trackEvent } = useAnalytics();

  const trackCustomEvent = () => {
    trackEvent({
      action: 'video_play',
      category: 'engagement',
      label: 'therapy_intro_video',
      value: 30 // giây
    });
  };
}
```

## 🎯 Các events được khuyến nghị track

### 1. User Registration Flow
- Bắt đầu registration
- Hoàn thành từng bước
- Gửi form thành công/thất bại

### 2. Login Flow  
- Click login button
- Login success/failure
- Logout

### 3. Therapy Booking
- View therapist list
- Filter therapists
- Book appointment
- Cancel appointment

### 4. Navigation
- Menu clicks
- Page transitions
- External link clicks

## 📊 Events có sẵn trong useAnalytics hook

### `trackButtonClick(buttonName, location?)`
Track các button clicks với context
```tsx
trackButtonClick('Book Appointment', 'Therapist Profile');
```

### `trackPageView(pageName)`
Track page views thủ công nếu cần
```tsx
trackPageView('Dashboard - Appointments');
```

### `trackFormSubmit(formName, success)`
Track form submissions
```tsx
trackFormSubmit('Contact Form', true);
```

### `trackUserAction(action, details?)`
Track các hành động user tổng quát
```tsx
trackUserAction('filter_therapists', 'anxiety,depression');
```

## 🔍 Debug và Testing

### 1. Real-time reports
- Vào Google Analytics > Reports > Realtime
- Kiểm tra events đang được gửi

### 2. DebugView (khuyến nghị)
- Bật debug mode trong GA4
- Xem chi tiết từng event

### 3. Browser DevTools
- Mở Network tab
- Filter theo "google-analytics" hoặc "gtag"
- Xem requests được gửi

## 🚫 Lưu ý quan trọng

### 1. GDPR & Privacy
- Thông báo cho users về việc sử dụng analytics
- Có thể cần cookie consent banner
- Tuân thủ các quy định về privacy

### 2. Data retention
- Cấu hình data retention phù hợp
- Không track thông tin cá nhân nhạy cảm

### 3. Environment
- Chỉ track ở production
- Có thể tạo separate GA property cho staging

## 📋 Checklist setup

- [ ] Tạo Google Analytics account & property
- [ ] Lấy Measurement ID (G-XXXXXXXXXX)
- [ ] Thêm `NEXT_PUBLIC_GA_ID` vào `.env.local`
- [ ] Kiểm tra Real-time reports
- [ ] Test một số events
- [ ] Cấu hình goals trong GA4 (nếu cần)
- [ ] Setup conversion tracking (nếu cần)

## 🔗 Resources hữu ích

- [Google Analytics 4 Documentation](https://developers.google.com/analytics/devguides/collection/ga4)
- [Next.js Analytics Guide](https://nextjs.org/docs/basic-features/script)
- [GA4 Events Reference](https://developers.google.com/analytics/devguides/collection/ga4/reference/events) 