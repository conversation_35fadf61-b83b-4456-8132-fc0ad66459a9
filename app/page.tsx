"use client"

import { useRouter } from "next/navigation"
import { useEffect, useRef, useState } from "react"
import { StickyBottomCTA } from "@/components/sticky-bottom-cta"
import { MobileStepTracker } from "@/components/mobile-step-tracker"
import { GetStartedForm } from "@/components/get-started-form"
import { DesktopLayout } from "@/components/desktop-layout"
import Image from "next/image"

// Type cho form data
interface FormData {
  state: string;
  therapyConcern?: string[];
  insuranceCarrier?: string;
  insuranceObject?: any;
}

export default function Home() {
  const router = useRouter()
  const formRef = useRef<HTMLFormElement>(null)
  const [formData, setFormData] = useState<FormData>({
    state: "",
    therapyConcern: [],
    insuranceCarrier: "",
    insuranceObject: null
  })

  // Detect if the app is being used as a PWA
  useEffect(() => {
    const isPWA = window.matchMedia("(display-mode: standalone)").matches
    if (isPWA) {
      // Could set some PWA-specific state here
    }
  }, [])

  const handleFormChange = (data: any) => {
    // Accept insuranceObject from child if present
    setFormData(data);
  }

  const handleSubmit = (data: any) => {
    // If insuranceObject is present, include it in onboardingData
    const onboardingData = {
      state: data.state,
      therapyConcern: data.therapyConcern,
      insuranceCarrier: data.insuranceCarrier,
      insuranceObject: data.insuranceObject || null,
    };
    localStorage.setItem("onboardingData", JSON.stringify(onboardingData));
    console.log("Data saved to localStorage", onboardingData);
    try {
      router.push("/therapists");
      console.log("Navigation requested");
    } catch (error) {
      console.error("Navigation error:", error);
    }
  }

  const handleGetStartedClick = () => {
    console.log("Get Started button clicked");
    // Only require state now
    if (!formData.state) {
      console.log("State is not selected:", formData);
      alert("Please select a state before proceeding");
      return;
    }
    console.log("Using form data:", formData);
    try {
      handleSubmit(formData);
    } catch (error) {
      console.error("Error submitting form:", error);
      alert("There was an error submitting the form. Please try again.");
    }
  }

  return (
    <>
      {/* Desktop Layout */}
      <DesktopLayout title="Welcome to Lavni" subtitle="Let's find the right mental health support for you.">
        <div className="bg-white p-8 rounded-lg shadow-sm">
          <MobileStepTracker currentStep={1} totalSteps={7} />
          <GetStartedForm 
            onSubmit={handleSubmit} 
            onChange={handleFormChange}
            initialValues={formData}
          />
          <button
            type="button"
            onClick={handleGetStartedClick}
            disabled={!formData.state}
            className="w-full h-14 mt-6 bg-[#F7903D] hover:bg-[#e67f2d] text-white rounded-md font-medium disabled:bg-gray-300 disabled:cursor-not-allowed"
          >
            Get Started
          </button>
        </div>
      </DesktopLayout>

      {/* Mobile Layout */}
      <main className="flex min-h-screen flex-col bg-white md:hidden">
        <div className="flex-1 px-4 py-6 pb-36">
          <div className="flex flex-col items-center mb-8">
            <Image src="/lavni-logo.png" alt="Lavni" width={100} height={100} priority className="mb-4" />
            <h1 className="text-2xl font-bold text-gray-900">Welcome to Lavni</h1>
            <p className="text-gray-600 mt-2 text-center">Let's find the right mental health support for you.</p>
          </div>

          <MobileStepTracker currentStep={1} totalSteps={7} />

          <GetStartedForm 
            onSubmit={handleSubmit} 
            onChange={handleFormChange}
            initialValues={formData}
          />
        </div>

        <StickyBottomCTA
          primaryText="Get Started"
          primaryAction={handleGetStartedClick}
          isDisabled={!formData.state}
        />
      </main>
    </>
  )
}
