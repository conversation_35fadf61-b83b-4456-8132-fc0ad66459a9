<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test User Flow - Lavni Therapy</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        button {
            background: #F7903D;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #e67f2d;
        }
        .success {
            color: #28a745;
            font-weight: bold;
        }
        .error {
            color: #dc3545;
            font-weight: bold;
        }
        .info {
            color: #17a2b8;
        }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .test-result.success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
        }
        .test-result.error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <h1>🧪 Lavni Therapy Booking System - User Flow Test</h1>
    
    <div class="test-section">
        <h2>1. Setup Test Data</h2>
        <p>This will populate localStorage with mock onboarding data and selected therapist.</p>
        <button onclick="setupTestData()">Setup Test Data</button>
        <div id="setup-result"></div>
    </div>

    <div class="test-section">
        <h2>2. Test Therapist Selection</h2>
        <p>Navigate to therapists page with insurance filtering.</p>
        <button onclick="testTherapistPage()">Go to Therapists Page</button>
        <div id="therapist-result"></div>
    </div>

    <div class="test-section">
        <h2>3. Test Calendar Functionality</h2>
        <p>Navigate to calendar page with selected therapist.</p>
        <button onclick="testCalendarPage()">Go to Calendar Page</button>
        <div id="calendar-result"></div>
    </div>

    <div class="test-section">
        <h2>4. API Tests</h2>
        <p>Test the backend API endpoints directly.</p>
        <button onclick="testAPIs()">Test APIs</button>
        <div id="api-result"></div>
    </div>

    <div class="test-section">
        <h2>5. Current localStorage Data</h2>
        <button onclick="showLocalStorage()">Show localStorage</button>
        <div id="localstorage-result"></div>
    </div>

    <div class="test-section">
        <h2>6. Clear Test Data</h2>
        <p>Clean up localStorage for fresh testing.</p>
        <button onclick="clearTestData()">Clear All Data</button>
        <div id="clear-result"></div>
    </div>

    <script>
        function setupTestData() {
            const onboardingData = {
                state: "North Carolina",
                insurance: "675c597705d202e19eab9d06", // Mock insurance ID
                age: "adult",
                concerns: ["Anxiety", "Depression"],
                previousTherapy: "yes",
                preferredGender: "no-preference"
            };

            const selectedTherapist = {
                _id: "675c597705d202e19eab9d06",
                name: "Beth Philipsen",
                firstname: "Beth",
                lastname: "Philipsen",
                title: "Licensed Therapist",
                image: "uploads/PROFILE_IMAGE/d5456db33f27a4d47c942f1323cf7b3f",
                specialties: ["Anxiety", "Depression", "Life Transitions"],
                bio: "Experienced therapist specializing in anxiety and depression.",
                gender: "Female",
                city: "Greensboro",
                state: "North Carolina",
                nextAvailability: "2025-05-28T11:30:00.000Z"
            };

            localStorage.setItem("onboardingData", JSON.stringify(onboardingData));
            localStorage.setItem("selectedTherapist", JSON.stringify(selectedTherapist));

            document.getElementById("setup-result").innerHTML = 
                '<div class="test-result success">✅ Test data setup complete!</div>';
        }

        function testTherapistPage() {
            window.open('/therapists', '_blank');
            document.getElementById("therapist-result").innerHTML = 
                '<div class="test-result success">✅ Therapists page opened in new tab</div>';
        }

        function testCalendarPage() {
            window.open('/calendar', '_blank');
            document.getElementById("calendar-result").innerHTML = 
                '<div class="test-result success">✅ Calendar page opened in new tab</div>';
        }

        async function testAPIs() {
            const resultDiv = document.getElementById("api-result");
            resultDiv.innerHTML = '<div class="info">Testing APIs...</div>';

            const tests = [];

            // Test 1: Backend therapists (using Next.js API proxy)
            try {
                const response = await fetch('/api/therapists?state=North+Carolina');
                const data = await response.json();
                tests.push({
                    name: "Backend Therapists API (via proxy)",
                    success: response.ok,
                    message: `Found ${data.data?.therapists?.length || 0} therapists`
                });
            } catch (error) {
                tests.push({
                    name: "Backend Therapists API (via proxy)",
                    success: false,
                    message: error.message
                });
            }

            // Test 2: Frontend availability API
            try {
                const therapistId = "675c597705d202e19eab9d06";
                const response = await fetch(`/api/therapists/${therapistId}/available-days?month=2025-05`);
                const data = await response.json();
                tests.push({
                    name: "Frontend Availability API",
                    success: response.ok,
                    message: `Working hours: ${data.workingHours?.length || 0}, Appointments: ${data.appointments?.length || 0}`
                });
            } catch (error) {
                tests.push({
                    name: "Frontend Availability API",
                    success: false,
                    message: error.message
                });
            }

            // Display results
            let html = '';
            tests.forEach(test => {
                const className = test.success ? 'success' : 'error';
                const icon = test.success ? '✅' : '❌';
                html += `<div class="test-result ${className}">${icon} ${test.name}: ${test.message}</div>`;
            });

            resultDiv.innerHTML = html;
        }

        function showLocalStorage() {
            const data = {
                onboardingData: localStorage.getItem("onboardingData"),
                selectedTherapist: localStorage.getItem("selectedTherapist"),
                appointmentDetails: localStorage.getItem("appointmentDetails")
            };

            let html = '<h4>Current localStorage:</h4>';
            for (const [key, value] of Object.entries(data)) {
                if (value) {
                    html += `<h5>${key}:</h5><pre>${JSON.stringify(JSON.parse(value), null, 2)}</pre>`;
                } else {
                    html += `<h5>${key}:</h5><p class="info">Not set</p>`;
                }
            }

            document.getElementById("localstorage-result").innerHTML = html;
        }

        function clearTestData() {
            localStorage.removeItem("onboardingData");
            localStorage.removeItem("selectedTherapist");
            localStorage.removeItem("appointmentDetails");
            
            document.getElementById("clear-result").innerHTML = 
                '<div class="test-result success">✅ All test data cleared!</div>';
        }

        // Auto-show localStorage on page load
        window.onload = function() {
            showLocalStorage();
        };
    </script>
</body>
</html> 