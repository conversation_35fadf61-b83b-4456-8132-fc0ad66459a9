interface MobileStepTrackerProps {
  currentStep: number
  totalSteps: number
}

export function MobileStepTracker({ currentStep, totalSteps }: MobileStepTrackerProps) {
  return (
    <div className="mb-6">
      <div className="flex items-center justify-between mb-2">
        <span className="text-sm font-medium text-gray-700">
          Step {currentStep} of {totalSteps}
          <span className="mx-2 text-gray-400">·</span>
          {Math.round((currentStep / totalSteps) * 100)}%
        </span>
      </div>
      <div className="w-full bg-gray-200 rounded-full h-2">
        <div className="bg-[#F7903D] h-2 rounded-full" style={{ width: `${(currentStep / totalSteps) * 100}%` }}></div>
      </div>
    </div>
  )
}
