import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    console.log('[SAVE-INSURANCE] Request received:', body);

    const {
      userId,
      insuranceData,
      eligibilityResult,
      isSecondary = false
    } = body;

    // Validate required fields
    if (!userId || !insuranceData) {
      return NextResponse.json(
        { error: 'Missing required fields: userId and insuranceData' },
        { status: 400 }
      );
    }

    // Call backend service to save insurance
    const backendUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3003';
    const endpoint = isSecondary ? '/api/insurance/secondary' : '/api/insurance/primary';
    
    const response = await fetch(`${backendUrl}${endpoint}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        userId,
        insuranceData,
        eligibilityResult
      }),
    });

    const result = await response.json();

    if (!response.ok) {
      console.error('[SAVE-INSURANCE] Backend error:', result);
      return NextResponse.json(
        { error: result.message || 'Failed to save insurance' },
        { status: response.status }
      );
    }

    console.log('[SAVE-INSURANCE] Insurance saved successfully:', result);
    
    return NextResponse.json({
      success: true,
      data: result.data
    });

  } catch (error) {
    console.error('[SAVE-INSURANCE] Error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
} 