import { MongoClient } from 'mongodb';
import dotenv from 'dotenv';

dotenv.config();

const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017';
const DB_NAME = process.env.MONGODB_DB || 'lavni_db';

// Map of insurance company names to their trading partner service IDs
// You'll need to fill this with actual data from Claim.MD
const tradingPartnerServiceIds: { [key: string]: string } = {
  'Blue Cross Blue Shield of North Carolina': '68069',
  'Aetna': '60054',
  'Cigna': '60054',
  'UnitedHealthcare': '60054',
  'Medicare': '60054',
  'Medicaid': '60054',
  // Add more mappings as needed
};

async function updateInsuranceCompanies() {
  const client = new MongoClient(MONGODB_URI);

  try {
    await client.connect();
    console.log('Connected to MongoDB');

    const db = client.db(DB_NAME);
    const collection = db.collection('insurancecompanies');

    // Get all insurance companies
    const companies = await collection.find({}).toArray();
    console.log(`Found ${companies.length} insurance companies`);

    // Update each company with its trading partner service ID
    for (const company of companies) {
      const tradingPartnerServiceId = tradingPartnerServiceIds[company.organizationName];
      
      if (tradingPartnerServiceId) {
        await collection.updateOne(
          { _id: company._id },
          { $set: { tradingPartnerServiceId } }
        );
        console.log(`Updated ${company.organizationName} with trading partner service ID: ${tradingPartnerServiceId}`);
      } else {
        console.warn(`No trading partner service ID found for ${company.organizationName}`);
      }
    }

    console.log('Update completed');
  } catch (error) {
    console.error('Error updating insurance companies:', error);
  } finally {
    await client.close();
    console.log('Disconnected from MongoDB');
  }
}

updateInsuranceCompanies(); 