# Lavni Backend

This is the backend server for the Lavni therapy application. It provides APIs for user management, appointment scheduling, and authentication.

## Tech Stack

- Node.js
- Express.js
- TypeScript
- MongoDB with Mongoose
- JWT for authentication

## Setup

1. Install dependencies:
```bash
npm install
```

2. Create a `.env` file in the root directory with the following variables:
```env
PORT=3003
MONGODB_URI=mongodb://localhost:27017/lavni
JWT_SECRET=your-super-secret-jwt-key-change-in-production
CORS_ORIGIN=http://localhost:3000
NODE_ENV=development
```

3. Start the development server:
```bash
npm run dev
```

## API Endpoints

### Authentication
- POST `/api/auth/register` - Register a new user
- POST `/api/auth/login` - Login user
- POST `/api/auth/verify-email` - Verify user email
- POST `/api/auth/google` - Google authentication

### Users
- GET `/api/users/profile` - Get user profile
- PUT `/api/users/profile` - Update user profile
- GET `/api/therapists` - Get list of therapists
- GET `/api/therapists/:id` - Get therapist details

### Appointments
- POST `/api/appointments` - Create new appointment
- GET `/api/appointments` - Get user appointments
- PUT `/api/appointments/:id` - Update appointment
- DELETE `/api/appointments/:id` - Cancel appointment
- GET `/api/appointments/therapist/:therapistId` - Get therapist appointments
- GET `/api/appointments/client/:clientId` - Get client appointments

## Development

The project uses TypeScript for type safety and better developer experience. The code is organized into the following structure:

```
src/
├── config/         # Configuration files
├── controllers/    # Route controllers
├── middleware/     # Custom middleware
├── models/         # Database models
├── routes/         # API routes
└── app.ts          # Express app setup
```

## Error Handling

The application uses a centralized error handling mechanism. All errors are processed through the `errorHandler` middleware which sends appropriate error responses.

## Authentication

JWT (JSON Web Tokens) is used for authentication. Tokens are issued upon successful login/registration and must be included in the Authorization header for protected routes:

```
Authorization: Bearer <token>
```

## Data Validation

Request validation is handled using Joi. Validation schemas are defined in the `middleware/validation.middleware.ts` file. 