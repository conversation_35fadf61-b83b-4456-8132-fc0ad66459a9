import express from 'express';
import { getAllTherapists, getTherapistById, getAvailableDays, getAvailableSlots, testTherapistData } from '../controllers/therapist.controller';

const router = express.Router();

// Public routes
router.get('/', getAllTherapists);
router.get('/test', testTherapistData);
router.get('/:id', getTherapistById);
router.get('/:id/available-days', getAvailableDays);
router.get('/:id/available-slots', getAvailableSlots);

export default router; 