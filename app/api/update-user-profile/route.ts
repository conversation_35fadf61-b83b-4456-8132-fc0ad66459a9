import { NextRequest, NextResponse } from 'next/server';

export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    console.log('[UPDATE-USER-PROFILE] Request received:', body);

    const { userId, clientType, parent, additionalData } = body;

    // Validate required fields
    if (!userId || !clientType) {
      return NextResponse.json(
        { error: 'Missing required fields: userId and clientType' },
        { status: 400 }
      );
    }

    // Call backend service
    const backendUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3003';
    const response = await fetch(`${backendUrl}/api/user-profile/update`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        userId,
        clientType,
        parent,
        additionalData
      }),
    });

    const result = await response.json();
    console.log('[UPDATE-USER-PROFILE] Backend response:', result);

    if (!response.ok) {
      return NextResponse.json(
        { error: result.message || 'Failed to update user profile' },
        { status: response.status }
      );
    }

    return NextResponse.json(result);

  } catch (error) {
    console.error('[UPDATE-USER-PROFILE] Error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
} 