import { NextRequest, NextResponse } from 'next/server';

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const body = await request.json();
    const { selectedMonth } = body;
    
    if (!selectedMonth) {
      return NextResponse.json({ message: 'selectedMonth is required' }, { status: 400 });
    }

    // Get authorization token from request headers or environment
    const authToken = request.headers.get('authorization') || process.env.LAVNI_API_TOKEN;
    
    if (!authToken) {
      return NextResponse.json({ message: 'Authorization token required' }, { status: 401 });
    }

    const productionApiUrl = 'https://api.lavnihealth.com/api/public/viewAllAppointmentsByTherapistIdForMonth';
    
    console.log('[PRODUCTION API] Calling Lavni Health API for therapist:', id);
    
    const response = await fetch(productionApiUrl, {
      method: 'POST',
      headers: {
        'Accept': 'application/json, text/plain, */*',
        'Content-Type': 'application/json',
        'Authorization': authToken,
        'Origin': 'https://mylavni.com',
        'User-Agent': 'Lavni-Client/1.0'
      },
      body: JSON.stringify({
        therapistId: id,
        selectedMonth: selectedMonth
      })
    });
    
    const data = await response.json();
    
    if (!response.ok) {
      console.error('[PRODUCTION API] Error from Lavni Health API:', data);
      return NextResponse.json(data, { status: response.status });
    }
    
    console.log('[PRODUCTION API] Successfully fetched appointments from Lavni Health API');
    return NextResponse.json(data);
  } catch (error) {
    console.error('[PRODUCTION API] Error calling Lavni Health API:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
} 