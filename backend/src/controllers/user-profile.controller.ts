import { Request, Response, NextFunction } from 'express';
import { User, IUser } from '../models/User';

export const updateUserProfile = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const { userId, clientType, parent, additionalData } = req.body;

    console.log('[USER-PROFILE] Updating user profile:', { userId, clientType, parent });

    // Validate required fields
    if (!userId) {
      return res.status(400).json({
        status: 'error',
        message: 'Missing required field: userId'
      });
    }

    if (!clientType) {
      return res.status(400).json({
        status: 'error',
        message: 'Missing required field: clientType'
      });
    }

    // Build update data object
    const updateData: any = { clientType };

    // Add parent information for minors
    if (clientType === 'minor' && parent) {
      console.log('[USER-PROFILE] Adding parent information for minor');
      updateData.parent = {
        parentFirstName: parent.parentFirstName,
        parentLastName: parent.parentLastName,
        parentEmail: parent.parentEmail,
        parentPhone: parent.parentPhone
      };
    }

    // Add any additional data (appointments, therapist info, etc.)
    if (additionalData) {
      Object.assign(updateData, additionalData);
    }

    console.log('[USER-PROFILE] Update data:', updateData);

    // Update user in database
    const updatedUser = await User.findByIdAndUpdate(
      userId,
      updateData,
      { new: true, runValidators: true }
    );

    if (!updatedUser) {
      return res.status(404).json({
        status: 'error',
        message: 'User not found'
      });
    }

    console.log('[USER-PROFILE] User profile updated successfully:', updatedUser._id);

    res.status(200).json({
      status: 'success',
      data: {
        user: {
          id: updatedUser._id,
          clientType: updatedUser.clientType,
          parent: updatedUser.parent,
          firstname: updatedUser.firstname,
          lastname: updatedUser.lastname,
          email: updatedUser.email
        }
      },
      message: 'User profile updated successfully'
    });

  } catch (error) {
    console.error('[USER-PROFILE] Error updating user profile:', error);
    next(error);
  }
};

export const getUserProfile = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const { userId } = req.params;

    console.log('[USER-PROFILE] Getting user profile:', userId);

    if (!userId) {
      return res.status(400).json({
        status: 'error',
        message: 'Missing required parameter: userId'
      });
    }

    const user = await User.findById(userId).select('-password');

    if (!user) {
      return res.status(404).json({
        status: 'error',
        message: 'User not found'
      });
    }

    res.status(200).json({
      status: 'success',
      data: { user }
    });

  } catch (error) {
    console.error('[USER-PROFILE] Error getting user profile:', error);
    next(error);
  }
}; 