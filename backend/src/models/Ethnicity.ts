import { Schema, model, Document } from 'mongoose';

export interface IEthnicity extends Document {
  name: string;
  description?: string;
  createdAt: Date;
  updatedAt: Date;
}

const ethnicitySchema = new Schema<IEthnicity>(
  {
    name: {
      type: String,
      required: true,
      unique: true,
      trim: true,
    },
    description: {
      type: String,
      trim: true,
    },
  },
  {
    timestamps: true,
  }
);

export const Ethnicity = model<IEthnicity>('Ethnicity', ethnicitySchema); 