import Image from "next/image"
import type { ReactNode } from "react"

interface DesktopLayoutProps {
  children: ReactNode
  showLogo?: boolean
  title?: string
  subtitle?: string
}

export function DesktopLayout({ children, showLogo = true, title, subtitle }: DesktopLayoutProps) {
  return (
    <div className="hidden md:flex min-h-screen bg-gray-50">
      {/* Left sidebar with logo and branding - fixed position to always remain in view */}
      <div className="w-1/3 bg-white p-10 flex flex-col fixed left-0 top-0 h-full">
        <div className="flex-1 flex flex-col justify-center items-center">
          {showLogo && (
            <div className="mb-8">
              <Image src="/lavni-logo.png" alt="Lavni" width={200} height={200} priority />
            </div>
          )}
          {title && <h1 className="text-3xl font-bold text-gray-900 text-center mb-4">{title}</h1>}
          {subtitle && <p className="text-xl text-gray-600 text-center max-w-md">{subtitle}</p>}
        </div>
        <div className="mt-auto">
          <p className="text-sm text-gray-500 text-center">© {new Date().getFullYear()} Lavni. All rights reserved.</p>
        </div>
      </div>

      {/* Main content area - adjust margin to account for fixed sidebar */}
      <div className="w-2/3 ml-[33.333%] h-screen overflow-y-auto">
        <div className="p-10">
          <div className="w-full">{children}</div>
        </div>
      </div>
    </div>
  )
}
