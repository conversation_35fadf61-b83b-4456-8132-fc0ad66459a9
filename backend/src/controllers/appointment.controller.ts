import { Request, Response, NextFunction } from 'express';
import { Appointment } from '../models/Appointment';
import { User } from '../models/User';
import { AppError } from '../middleware/error.middleware';

export const createAppointment = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const appointment = await Appointment.create({
      ...req.body,
      createdBy: req.user._id,
    });

    res.status(201).json({
      status: 'success',
      data: { appointment },
    });
  } catch (error) {
    next(error);
  }
};

export const getAppointments = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const appointments = await Appointment.find({
      $or: [
        { clientId: req.user._id },
        { therapistId: req.user._id },
      ],
    }).sort({ start: 1 });

    res.status(200).json({
      status: 'success',
      results: appointments.length,
      data: { appointments },
    });
  } catch (error) {
    next(error);
  }
};

export const getAppointmentById = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const appointment = await Appointment.findById(req.params.id);

    if (!appointment) {
      throw new AppError('Appointment not found', 404);
    }

    // Check if user has permission to view this appointment
    if (
      appointment.clientId.toString() !== req.user._id.toString() &&
      appointment.therapistId.toString() !== req.user._id.toString()
    ) {
      throw new AppError('Not authorized to view this appointment', 403);
    }

    res.status(200).json({
      status: 'success',
      data: { appointment },
    });
  } catch (error) {
    next(error);
  }
};

export const updateAppointment = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const appointment = await Appointment.findById(req.params.id);

    if (!appointment) {
      throw new AppError('Appointment not found', 404);
    }

    // Check if user has permission to update this appointment
    if (
      appointment.clientId.toString() !== req.user._id.toString() &&
      appointment.therapistId.toString() !== req.user._id.toString()
    ) {
      throw new AppError('Not authorized to update this appointment', 403);
    }

    const updatedAppointment = await Appointment.findByIdAndUpdate(
      req.params.id,
      req.body,
      { new: true, runValidators: true }
    );

    res.status(200).json({
      status: 'success',
      data: { appointment: updatedAppointment },
    });
  } catch (error) {
    next(error);
  }
};

export const deleteAppointment = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const appointment = await Appointment.findById(req.params.id);

    if (!appointment) {
      throw new AppError('Appointment not found', 404);
    }

    // Check if user has permission to delete this appointment
    if (
      appointment.clientId.toString() !== req.user._id.toString() &&
      appointment.therapistId.toString() !== req.user._id.toString()
    ) {
      throw new AppError('Not authorized to delete this appointment', 403);
    }

    await appointment.deleteOne();

    res.status(204).json({
      status: 'success',
      data: null,
    });
  } catch (error) {
    next(error);
  }
};

export const getTherapistAppointments = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const appointments = await Appointment.find({
      therapistId: req.params.therapistId,
      status: { $ne: 'CANCELLED' },
    }).sort({ start: 1 });

    res.status(200).json({
      status: 'success',
      results: appointments.length,
      data: { appointments },
    });
  } catch (error) {
    next(error);
  }
};

export const getClientAppointments = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const appointments = await Appointment.find({
      clientId: req.params.clientId,
      status: { $ne: 'CANCELLED' },
    }).sort({ start: 1 });

    res.status(200).json({
      status: 'success',
      results: appointments.length,
      data: { appointments },
    });
  } catch (error) {
    next(error);
  }
}; 