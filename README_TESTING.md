# 🧪 Testing the Lavni Therapy Booking System

## Quick Start Testing

### 1. Automated Tests
```bash
# Run the comprehensive test script
./test-calendar.sh
```

### 2. Interactive Testing
Open in browser: `http://localhost:3000/test-user-flow.html`

## What's Been Fixed & Implemented

### ✅ Issues Resolved
1. **Missing API Routes**: Created proxy routes for therapist availability
2. **Compilation Errors**: Fixed mobile-calendar-selector import issues  
3. **Timezone Handling**: Implemented 4-hour offset for time display
4. **Insurance Filtering**: Added backend and frontend insurance filtering
5. **Frontend Logic Migration**: Moved calendar calculations from backend to frontend

### ✅ Features Working
- **Therapist Selection**: Filter by state and insurance
- **Calendar Display**: Shows available dates with proper highlighting
- **Time Slots**: Displays available times with timezone offset
- **Insurance Integration**: Filters therapists by insurance companies
- **Mobile Responsive**: Works on both desktop and mobile devices

## Testing Scenarios

### Scenario 1: Complete User Flow
1. Go to `http://localhost:3000/test-user-flow.html`
2. Click "Setup Test Data" to populate localStorage
3. Click "Go to Therapists Page" to test therapist selection
4. Click "Go to Calendar Page" to test appointment booking
5. Use "Test APIs" to verify backend connectivity

### Scenario 2: Manual Testing
1. Navigate to `http://localhost:3000`
2. Complete onboarding with:
   - State: "North Carolina" 
   - Insurance: Any option
   - Age: Adult
3. Select a therapist and click "Schedule"
4. Choose a date and time slot
5. Confirm appointment

### Scenario 3: API Testing
```bash
# Test backend therapists
curl "http://localhost:3003/api/users/therapists?state=North+Carolina"

# Test frontend availability
curl "http://localhost:3000/api/therapists/675c597705d202e19eab9d06/available-days?month=2025-05"
```

## Key Features to Test

### 🕐 Timezone Handling
- Times display with 4-hour offset (appear 4 hours earlier)
- Example: 6:30 PM UTC shows as 2:30 PM
- Check browser console for `[DEBUG]` logs

### 🏥 Insurance Filtering  
- Therapists filtered by selected insurance
- Insurance companies displayed on therapist cards
- Backend query includes insurance parameter

### 📅 Calendar Functionality
- Available dates highlighted in orange
- Past dates disabled
- Blocked dates not selectable
- Time slots properly formatted (12-hour format)

### 📱 Mobile Responsiveness
- Calendar works on mobile devices
- Touch interactions for date/time selection
- Responsive layout adapts to screen size

## Debug Information

### Frontend Logs (Browser Console)
```
[FRONTEND] Fetching therapist data...
[FRONTEND] Calculated available dates: X
[DEBUG] Original UTC date: 2025-05-28T18:30:00.000Z
[DEBUG] After -4 hours: 2025-05-28T14:30:00.000Z
[DEBUG] Final display time: 2:30 PM
```

### Backend Logs (Terminal)
```
[BACKEND] Getting raw data for month: 2025-05
--- BACKEND LOG ---
Therapist: Beth Philipsen
  nextAvailability: 2025-05-28T11:30:00.000Z
```

## Test Data

### Sample Therapist IDs
- **Beth Philipsen**: `675c597705d202e19eab9d06`
- **Raymond Benton**: `665e1fad12855f42904e2fa0`
- **Carmen Armour**: `67380de7109d25b2d746f608`

### Mock Data Structure
```json
{
  "onboardingData": {
    "state": "North Carolina",
    "insurance": "675c597705d202e19eab9d06",
    "age": "adult"
  },
  "selectedTherapist": {
    "_id": "675c597705d202e19eab9d06",
    "name": "Beth Philipsen",
    "specialties": ["Anxiety", "Depression"]
  }
}
```

## Performance Metrics

### Current Test Results
- ✅ Backend: 8 therapists loaded
- ✅ Frontend: All pages accessible (HTTP 200)
- ✅ API: Availability data with 16 working hour periods
- ✅ Calendar: Responsive and functional

### Expected Response Times
- Therapist list: < 1 second
- Calendar data: < 500ms
- Time slot calculation: < 100ms

## Troubleshooting

### Common Issues
| Issue | Solution |
|-------|----------|
| Calendar not loading | Check API routes in `app/api/therapists/` |
| No time slots | Verify therapist has working hours |
| Wrong times | Check `convertUTCDateToLocalDate()` function |
| Insurance not filtering | Verify insurance IDs in database |

### Quick Fixes
```bash
# Restart servers if needed
pkill -f "next dev"
npm run dev

# Check server status
ps aux | grep "next dev"
curl http://localhost:3000/api/therapists/675c597705d202e19eab9d06/available-days?month=2025-05
```

## Success Criteria

### ✅ All Tests Passing
- [x] Backend connectivity
- [x] Frontend accessibility  
- [x] API proxy routes working
- [x] Calendar displays correctly
- [x] Time slots with proper offset
- [x] Insurance filtering functional
- [x] Mobile responsive design

### ✅ User Experience
- [x] Smooth therapist selection flow
- [x] Intuitive calendar interface
- [x] Clear time slot display
- [x] Proper error handling
- [x] Loading states implemented

## Next Steps

1. **Production Testing**: Test with real insurance data
2. **Load Testing**: Test with more therapists and appointments
3. **Browser Testing**: Verify across different browsers
4. **Accessibility**: Test with screen readers
5. **Performance**: Optimize API response times

---

## Quick Commands

```bash
# Run all tests
./test-calendar.sh

# Open test interface
open http://localhost:3000/test-user-flow.html

# Check logs
tail -f /dev/null  # Frontend logs in browser console

# Test specific API
curl "http://localhost:3000/api/therapists/675c597705d202e19eab9d06/available-days?month=2025-05"
```

**Happy Testing! 🎉** 