"use client"

import { useRouter } from "next/navigation"
import { useState } from "react"
import { StickyBottomCTA } from "@/components/sticky-bottom-cta"
import { MobileStepTracker } from "@/components/mobile-step-tracker"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Label } from "@/components/ui/label"
import { DesktopLayout } from "@/components/desktop-layout"
import { MobileHeader } from "@/components/mobile-header"
import { Button } from "@/components/ui/button"
import { ChevronLeft } from "lucide-react"

export default function WhoIsThisForPage() {
  const router = useRouter()
  const [selectedOption, setSelectedOption] = useState<string | null>(null)

  const handleContinue = () => {
    if (selectedOption) {
      // Store the selection
      localStorage.setItem("appointmentFor", selectedOption)

      // Route based on selection
      if (selectedOption === "adult-self") {
        router.push("/registration-adult")
      } else {
        router.push("/registration-minor")
      }
    }
  }

  return (
    <>
      {/* Desktop Layout */}
      <DesktopLayout title="Who Is This For?" subtitle="Please select who will be receiving therapy.">
        <div className="bg-white p-8 rounded-lg shadow-sm">
          <MobileStepTracker currentStep={4} totalSteps={7} />

          <div className="mt-6">
            <RadioGroup value={selectedOption || ""} onValueChange={setSelectedOption} className="space-y-4">
              <div 
                className="flex items-start space-x-3 border border-gray-200 rounded-lg p-4 hover:border-[#F7903D] transition-colors cursor-pointer"
                onClick={() => setSelectedOption("adult-self")}
              >
                <RadioGroupItem
                  value="adult-self"
                  id="desktop-adult-self"
                  className="mt-1 border-[#64748B] text-[#F7903D]"
                />
                <div className="flex-1">
                  <Label htmlFor="desktop-adult-self" className="text-base font-medium cursor-pointer">
                    Adult (18+) scheduling for self
                  </Label>
                  <p className="text-sm text-gray-600 mt-1">I am an adult seeking therapy for myself</p>
                </div>
              </div>

              <div 
                className="flex items-start space-x-3 border border-gray-200 rounded-lg p-4 hover:border-[#F7903D] transition-colors cursor-pointer"
                onClick={() => setSelectedOption("adult-child")}
              >
                <RadioGroupItem
                  value="adult-child"
                  id="desktop-adult-child"
                  className="mt-1 border-[#64748B] text-[#F7903D]"
                />
                <div className="flex-1">
                  <Label htmlFor="desktop-adult-child" className="text-base font-medium cursor-pointer">
                    Adult scheduling for child (under 18)
                  </Label>
                  <p className="text-sm text-gray-600 mt-1">I am an adult seeking therapy for my child</p>
                </div>
              </div>

              <div 
                className="flex items-start space-x-3 border border-gray-200 rounded-lg p-4 hover:border-[#F7903D] transition-colors cursor-pointer"
                onClick={() => setSelectedOption("minor-self")}
              >
                <RadioGroupItem
                  value="minor-self"
                  id="desktop-minor-self"
                  className="mt-1 border-[#64748B] text-[#F7903D]"
                />
                <div className="flex-1">
                  <Label htmlFor="desktop-minor-self" className="text-base font-medium cursor-pointer">
                    Minor (under 18) scheduling for self
                  </Label>
                  <p className="text-sm text-gray-600 mt-1">I am under 18 and seeking therapy for myself</p>
                </div>
              </div>
            </RadioGroup>
          </div>

          <div className="flex gap-3 mt-8">
            <Button variant="outline" onClick={() => router.back()} className="flex-1 border-[#64748B] text-[#64748B]">
              <ChevronLeft size={16} className="mr-1" />
              Back
            </Button>
            <Button
              onClick={handleContinue}
              disabled={!selectedOption}
              className="flex-1 bg-[#F7903D] hover:bg-[#e67f2d] text-white"
            >
              Continue
            </Button>
          </div>
        </div>
      </DesktopLayout>

      {/* Mobile Layout */}
      <main className="flex min-h-screen flex-col bg-white md:hidden">
        <MobileHeader showBackButton title="Who Is This For?" />
        <div className="flex-1 px-4 py-6 pb-36">
          <p className="text-gray-600 mb-6">Please select who will be receiving therapy.</p>

          <MobileStepTracker currentStep={4} totalSteps={7} />

          <div className="mt-6">
            <RadioGroup value={selectedOption || ""} onValueChange={setSelectedOption} className="space-y-4">
              <div 
                className="flex items-start space-x-3 border border-gray-200 rounded-lg p-4 hover:border-[#F7903D] transition-colors cursor-pointer"
                onClick={() => setSelectedOption("adult-self")}
              >
                <RadioGroupItem value="adult-self" id="adult-self" className="mt-1 border-[#64748B] text-[#F7903D]" />
                <div className="flex-1">
                  <Label htmlFor="adult-self" className="text-base font-medium cursor-pointer">
                    Adult (18+) scheduling for self
                  </Label>
                  <p className="text-sm text-gray-600 mt-1">I am an adult seeking therapy for myself</p>
                </div>
              </div>

              <div 
                className="flex items-start space-x-3 border border-gray-200 rounded-lg p-4 hover:border-[#F7903D] transition-colors cursor-pointer"
                onClick={() => setSelectedOption("adult-child")}
              >
                <RadioGroupItem value="adult-child" id="adult-child" className="mt-1 border-[#64748B] text-[#F7903D]" />
                <div className="flex-1">
                  <Label htmlFor="adult-child" className="text-base font-medium cursor-pointer">
                    Adult scheduling for child (under 18)
                  </Label>
                  <p className="text-sm text-gray-600 mt-1">I am an adult seeking therapy for my child</p>
                </div>
              </div>

              <div 
                className="flex items-start space-x-3 border border-gray-200 rounded-lg p-4 hover:border-[#F7903D] transition-colors cursor-pointer"
                onClick={() => setSelectedOption("minor-self")}
              >
                <RadioGroupItem value="minor-self" id="minor-self" className="mt-1 border-[#64748B] text-[#F7903D]" />
                <div className="flex-1">
                  <Label htmlFor="minor-self" className="text-base font-medium cursor-pointer">
                    Minor (under 18) scheduling for self
                  </Label>
                  <p className="text-sm text-gray-600 mt-1">I am under 18 and seeking therapy for myself</p>
                </div>
              </div>
            </RadioGroup>
          </div>
        </div>

        <StickyBottomCTA
          primaryText="Continue"
          primaryAction={handleContinue}
          secondaryText="Back"
          secondaryAction={() => router.back()}
          isDisabled={!selectedOption}
        />
      </main>
    </>
  )
}
