"use client"

import Image from "next/image"
import { ChevronLeft } from "lucide-react"
import { Button } from "@/components/ui/button"
import { useRouter } from "next/navigation"

interface MobileHeaderProps {
  showBackButton?: boolean
  title?: string
  showLogo?: boolean
}

export function MobileHeader({ showBackButton = false, title, showLogo = false }: MobileHeaderProps) {
  const router = useRouter()

  return (
    <div className="flex items-center justify-between py-4 px-4 border-b border-gray-200 md:hidden">
      <div className="flex items-center">
        {showBackButton && (
          <Button variant="ghost" size="sm" onClick={() => router.back()} className="mr-2 h-8 w-8 p-0 text-gray-500">
            <ChevronLeft size={20} />
            <span className="sr-only">Back</span>
          </Button>
        )}
        {showLogo && (
          <div className="h-8 w-auto">
            <Image src="/lavni-logo.png" alt="Lavni" width={32} height={32} />
          </div>
        )}
        {title && <h1 className="text-lg font-semibold">{title}</h1>}
      </div>
    </div>
  )
}
