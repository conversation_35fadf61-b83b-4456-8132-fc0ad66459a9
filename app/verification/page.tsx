"use client"

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import { StickyBottomCTA } from "@/components/sticky-bottom-cta"
import { MobileStepTracker } from "@/components/mobile-step-tracker"
import { VerificationCodeInput } from "@/components/verification-code-input"
import { Button } from "@/components/ui/button"

export default function VerificationPage() {
  const router = useRouter();
  const [userData, setUserData] = useState<any>(null);
  const [verificationCode, setVerificationCode] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [time, setTime] = useState(30);
  const [reset, setReset] = useState(false);
  const [isVerifyingInsurance, setIsVerifyingInsurance] = useState(false);
  const [isCreatingInsurance, setIsCreatingInsurance] = useState(false);
  const [isHydrated, setIsHydrated] = useState(false);
  const [isGoogleAutoVerifying, setIsGoogleAutoVerifying] = useState(false);

  useEffect(() => {
    // Mark as hydrated to prevent hydration mismatch
    setIsHydrated(true);

    const storedUser = localStorage.getItem("registeredUser");
    const storedUserData = localStorage.getItem("userData");
    
    if (storedUser) {
      setUserData(JSON.parse(storedUser));
      console.log('[VERIFICATION] Loaded user data:', JSON.parse(storedUser));

      // Check if this is a Google registration
      if (storedUserData) {
        try {
          const userData = JSON.parse(storedUserData);
          if (userData.registrationType === 'google') {
            console.log('[VERIFICATION] Google registration detected - auto-verifying...');
            setIsGoogleAutoVerifying(true);
            toast.success("✅ Verified with Google!");
            
            // Auto verify after 1.5 seconds
            setTimeout(() => {
              handleAutoVerifyGoogle();
            }, 1500);
            return;
          }
        } catch (e) {
          console.error('[VERIFICATION] Error parsing userData:', e);
        }
      }

      // For normal email registration, show verification code input
      toast.info("Please enter the verification code sent to your phone");
    } else {
      router.push("/registration-adult");
    }
  }, [router]);

  // Timer for resend functionality
  useEffect(() => {
    let timer: NodeJS.Timeout;

    const startTimer = () => {
      timer = setInterval(() => {
        setTime(prevTime => prevTime - 1);
      }, 1000);
    };

    const stopTimer = () => {
      clearInterval(timer);
      setReset(true);
    };

    if (time > 0) {
      startTimer();
    } else {
      stopTimer();
    }

    return () => {
      clearInterval(timer);
    };
  }, [time]);

  const handleReset = () => {
    setTime(60);
    setReset(false);
  };

  // Auto verify function for Google registration
  const handleAutoVerifyGoogle = async () => {
    console.log('[GOOGLE-VERIFY] Starting auto-verification for Google registration');
    
    try {
      // Store verification success (same as normal flow)
      localStorage.setItem("phoneVerified", "true");
      
      // For Google registration, we skip phone verification and proceed directly
      await checkInsuranceEligibility();
      
      // Navigate to confirmation page (same as successful verification)
      setTimeout(() => {
        console.log('[GOOGLE-VERIFY] Auto-verification complete, navigating to confirmation');
        router.push("/confirmation");
      }, 2000); // Give time for insurance check to complete
      
    } catch (error) {
      console.error('[GOOGLE-VERIFY] Error during auto-verification:', error);
      toast.error("Auto-verification failed. Please contact support.");
    }
  };

  const handleSendCode = async () => {
    if (!userData || !userData._id) {
      setError("User ID not found.");
      return;
    }
    
    handleReset();
    setIsSubmitting(true);
    setError(null);
    
    try {
      console.log('[VERIFICATION] Resending code for user:', userData._id);
      
      // Use Lavni API to resend verification code
      const response = await fetch("https://api.lavnihealth.com/api/public/resendVerificationCode", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ 
          userId: userData._id
        }),
      });
      
      const data = await response.json();
      console.log('[VERIFICATION] Resend response:', data);
      
      if (response.ok && data.success) {
        toast.success("Your Lavni verification code has been sent. This code will expire in 5 minutes.");
      } else {
        setError(data.message || "Failed to send verification code.");
        toast.error(data.message || "Failed to send verification code.");
      }
    } catch (err) {
      console.error('[VERIFICATION] Resend error:', err);
      setError("An error occurred while sending the verification code.");
      toast.error("An error occurred while sending the verification code.");
    } finally {
      setIsSubmitting(false);
    }
  };

  // New function to create insurance record in Lavni system
  const createInsuranceRecord = async (userFormData: any, insuranceCompany: any, eligibilityResult: any, userId: string) => {
    console.log('[INSURANCE_CREATE] Starting insurance record creation process...');
    console.log('[INSURANCE_CREATE] User data:', userFormData);
    console.log('[INSURANCE_CREATE] Insurance company:', insuranceCompany);
    console.log('[INSURANCE_CREATE] User ID:', userId);

    try {
      // Prepare payload for our backend API (not Lavni directly)
      const insurancePayload = {
        clientId: userId,
        insuranceCompanyId: insuranceCompany._id,
        subscriber: {
          memberId: userFormData.memberId,
          firstName: userFormData.firstName,
          lastName: userFormData.lastName,
          address: {
            state: userFormData.state || 'North Carolina'
          }
        }
      };

      console.log('[INSURANCE_CREATE] Prepared payload for backend API');

      // Call our backend API instead of Lavni directly
      console.log('[INSURANCE_CREATE] 🚀 Calling backend insurance creation API...');
      const createResponse = await fetch("/api/create-insurance", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(insurancePayload),
      });

      const createResult = await createResponse.json();
      
      if (createResponse.ok && createResult.success) {
        console.log('[INSURANCE_CREATE] ✅ Insurance record created successfully!');
        toast.success("Insurance record created in Lavni system!");
        return { success: true, data: createResult };
      } else {
        console.log('[INSURANCE_CREATE] ❌ Insurance creation failed');
        // Only log basic error info on frontend - detailed logging happens on backend
        console.log('[INSURANCE_CREATE] Error message:', createResult.error);
        
        toast.warning("Insurance creation failed - our team will follow up with you shortly");
        return { success: false, error: createResult.error };
      }

    } catch (error) {
      console.error('[INSURANCE_CREATE] ❌ Exception during insurance creation:', error);
      toast.error("Failed to create insurance record");
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  };

  // New function to check insurance eligibility
  const checkInsuranceEligibility = async () => {
    const storedUserData = localStorage.getItem("userData");
    if (!storedUserData) {
      console.log('[INSURANCE] No user data found for eligibility check');
      return;
    }

    try {
      const userFormData = JSON.parse(storedUserData);
      console.log('[INSURANCE] Starting eligibility check for:', userFormData);

      // Check if we have all required insurance data
      if (!userFormData.insurance || !userFormData.memberId) {
        console.log('[INSURANCE] Missing insurance data, skipping eligibility check');
        return;
      }

      setIsVerifyingInsurance(true);
      toast.info("Verifying your insurance benefits...");

      // Get insurance company trading partner ID from backend
      let insuranceData;
      try {
        const backendUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3003';
        const insuranceResponse = await fetch(`${backendUrl}/api/insurance-companies`);
        insuranceData = await insuranceResponse.json();
        
        if (insuranceData.status !== 'success' || !insuranceData.data) {
          console.error('[INSURANCE] Failed to fetch insurance companies:', insuranceData);
          throw new Error('Insurance companies API failed');
        }
      } catch (apiError) {
        console.error('[INSURANCE] Backend API not available, using fallback:', apiError);
        // Fallback: Skip insurance verification if backend is not available
        toast.warning("Insurance verification temporarily unavailable - proceeding without verification");
        return;
      }

      const insuranceCompany = insuranceData.data.find(
        (company: any) => company.organizationName === userFormData.insurance
      );

      if (!insuranceCompany) {
        console.log('[INSURANCE] Insurance company not found:', userFormData.insurance);
        toast.warning("Insurance company not found in our system - proceeding without verification");
        return;
      }

      // Prepare eligibility check payload
      const today = new Date();
      const serviceDate = `${today.getFullYear()}${String(today.getMonth() + 1).padStart(2, '0')}${String(today.getDate()).padStart(2, '0')}`;
      
      const eligibilityPayload = {
        firstName: userFormData.firstName,
        lastName: userFormData.lastName,
        insuranceCompanyId: insuranceCompany.tradingPartnerServiceId,
        dateOfBirth: userFormData.dob.replace(/-/g, ""), // Convert YYYY-MM-DD to YYYYMMDD
        relationshipCode: "18", // Self
        serviceDate,
        gender: userFormData.gender === "male" ? "M" : userFormData.gender === "female" ? "F" : "U",
        memberId: userFormData.memberId,
      };

      console.log('[INSURANCE] Sending eligibility check:', eligibilityPayload);

      // Call eligibility API
      const eligibilityResponse = await fetch("/api/check-eligibility", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(eligibilityPayload),
      });

      const eligibilityResult = await eligibilityResponse.json();
      console.log('[INSURANCE] Eligibility result:', eligibilityResult);

      // Store eligibility result for confirmation page
      localStorage.setItem("eligibilityResult", JSON.stringify(eligibilityResult));

      // Save insurance to database if user ID is available
      const registeredUser = localStorage.getItem("registeredUser");
      if (registeredUser) {
        try {
          const user = JSON.parse(registeredUser);
          const userId = user._id;
          
          if (userId && eligibilityResult.eligibilityStatus === "ACTIVE") {
            const insuranceData = {
              carrier: userFormData.insurance,
              memberId: userFormData.memberId,
              firstName: userFormData.firstName,
              lastName: userFormData.lastName,
              dob: userFormData.dob,
              state: userFormData.state
            };

            await fetch("/api/save-insurance", {
              method: "POST",
              headers: { "Content-Type": "application/json" },
              body: JSON.stringify({
                userId,
                insuranceData,
                eligibilityResult,
                isSecondary: false
              }),
            });
            
            console.log('[INSURANCE] Primary insurance saved to database during verification');
          }
        } catch (error) {
          console.error('[INSURANCE] Error saving insurance to database:', error);
        }
      }

      if (eligibilityResult.eligibilityStatus === "ACTIVE") {
        toast.success("Insurance verified successfully!");
        console.log('[INSURANCE] Insurance verified - Copay:', eligibilityResult.copayAmount);
        
        // NEW: Create insurance record in Lavni system after successful eligibility check
        console.log('[INSURANCE] 🚀 Starting insurance record creation process...');
        setIsCreatingInsurance(true);
        toast.info("Creating insurance record in Lavni system...");
        
        const registeredUser = localStorage.getItem("registeredUser");
        if (registeredUser) {
          const user = JSON.parse(registeredUser);
          const userId = user._id;
          
          if (userId) {
            console.log('[INSURANCE] Calling createInsuranceRecord with userId:', userId);
            const createResult = await createInsuranceRecord(userFormData, insuranceCompany, eligibilityResult, userId);
            
            if (createResult.success) {
              console.log('[INSURANCE] ✅ Insurance record creation completed successfully');
            } else {
              console.log('[INSURANCE] ❌ Insurance record creation failed:', createResult.error);
            }
          } else {
            console.log('[INSURANCE] ⚠️ No userId available for insurance record creation');
          }
        } else {
          console.log('[INSURANCE] ⚠️ No registered user data available for insurance record creation');
        }
        
        setIsCreatingInsurance(false);
      } else {
        toast.warning("Insurance verification completed - please review on confirmation page");
        console.log('[INSURANCE] Insurance not verified or inactive - skipping insurance record creation');
      }

    } catch (error) {
      console.error('[INSURANCE] Eligibility check error:', error);
      toast.error("Unable to verify insurance at this time");
    } finally {
      setIsVerifyingInsurance(false);
    }
  };

  const handleVerifyCode = async () => {
    if (!userData || !userData._id) {
      setError("User ID not found.");
      toast.error("User ID not found.");
      return;
    }
    
    if (!verificationCode) {
      setError("Verification code is required.");
      toast.error("Verification code is required.");
      return;
    }
    
    setIsSubmitting(true);
    setError(null);
    
    try {
      console.log('[VERIFICATION] Verifying code for user:', userData._id);
      console.log('[VERIFICATION] Verification code:', verificationCode);
      
      // Use Lavni API to verify the code
      const verifyResponse = await fetch("https://api.lavnihealth.com/api/public/verifyByCode", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          verificationCode: verificationCode,
          userId: userData._id
        }),
      });
      
      const verifyData = await verifyResponse.json();
      console.log('[VERIFICATION] Verify response:', verifyData);
      
      if (verifyResponse.ok && verifyData.success) {
        toast.success("Phone number verified successfully!");
        
        // Store verification success
        localStorage.setItem("phoneVerified", "true");
        
        // Automatically check insurance eligibility after successful verification
        await checkInsuranceEligibility();
        
        // Redirect to confirmation page
        setTimeout(() => {
          router.push("/confirmation");
        }, 2000); // Give time for insurance check to complete
      } else {
        setError(verifyData.message || "Invalid or expired verification code");
        toast.error(verifyData.message || "Invalid or expired verification code");
      }
    } catch (err) {
      console.error('[VERIFICATION] Verify error:', err);
      setError("An error occurred while verifying the code.");
      toast.error("An error occurred while verifying the code.");
    } finally {
      setIsSubmitting(false);
    }
  };

  // Show loading state until hydrated to prevent hydration mismatch
  if (!isHydrated) {
    return (
      <main className="flex min-h-screen flex-col bg-white">
        <div className="flex-1 px-4 py-6 pb-36">
          <div className="mb-6">
            <h1 className="text-2xl font-bold text-gray-900">Verify Your Account</h1>
            <p className="text-gray-600 mt-2">
              Thank you for signing up with Lavni! To proceed with your account you have to verify your phone number. Please enter the OTP sent to your phone.
            </p>
          </div>
          <MobileStepTracker currentStep={6} totalSteps={7} />
          <div className="mt-8">
            <div className="flex justify-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#F7903D]"></div>
            </div>
          </div>
        </div>
      </main>
    );
  }

  return (
    <main className="flex min-h-screen flex-col bg-white">
      <div className="flex-1 px-4 py-6 pb-36">
        <div className="mb-6">
          <h1 className="text-2xl font-bold text-gray-900">Verify Your Account</h1>
          {isGoogleAutoVerifying ? (
            <p className="text-gray-600 mt-2">
              ✅ <span className="font-medium text-green-600">Verified with Google!</span> 
              <br />
              Proceeding with your registration automatically...
            </p>
          ) : (
            <p className="text-gray-600 mt-2">
              Thank you for signing up with Lavni! To proceed with your account you have to verify your phone number. Please enter the OTP sent to{" "}
              {userData?.primaryPhone ? (
                <span className="font-medium">{userData.primaryPhone}</span>
              ) : (
                "your phone"
              )}
            </p>
          )}
          {isVerifyingInsurance && (
            <p className="text-blue-600 mt-2 text-sm">
              ✓ Phone verified - now checking your insurance benefits...
            </p>
          )}
          {isCreatingInsurance && (
            <p className="text-green-600 mt-2 text-sm">
              ✓ Insurance verified - now creating your insurance record...
            </p>
          )}
        </div>

        <MobileStepTracker currentStep={6} totalSteps={7} />

        <div className="mt-8">
          {!isGoogleAutoVerifying && (
            <>
              <VerificationCodeInput value={verificationCode} onChange={setVerificationCode} length={6} disabled={isSubmitting || isVerifyingInsurance || isCreatingInsurance} />
              
              <p className="text-center text-sm text-gray-500 mt-3">
                💡 Tip: You can paste your 6-digit code directly into any field
              </p>

              <div className="mt-6 text-center">
                <p className="text-center text-sm text-gray-600">
                  {time > 0 && <span>Wait: {time}s to </span>}
                  <Button
                    variant="link"
                    onClick={handleSendCode}
                    disabled={isSubmitting || !reset || isVerifyingInsurance || isCreatingInsurance}
                    className={`text-[#F7903D] hover:text-[#e67f2d] p-0 h-auto ${!reset ? 'opacity-50 cursor-not-allowed' : ''}`}
                  >
                    {isSubmitting ? "Sending..." : "Resend code"}
                  </Button>
                </p>
              </div>
            </>
          )}

          {isGoogleAutoVerifying && (
            <div className="flex flex-col items-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#F7903D] mb-4"></div>
              <p className="text-sm text-gray-600 text-center">
                Automatically verifying with Google...
              </p>
            </div>
          )}
          
          {error && (
            <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-md">
              <p className="text-sm text-red-600">{error}</p>
            </div>
          )}
        </div>
      </div>

      {!isGoogleAutoVerifying && (
        <StickyBottomCTA
          primaryText={
            isCreatingInsurance 
              ? "Creating Insurance Record..." 
              : isVerifyingInsurance 
                ? "Verifying Insurance..." 
                : "Verify"
          }
          primaryAction={handleVerifyCode}
          secondaryText="Back"
          secondaryAction={() => router.back()}
          isDisabled={verificationCode.length !== 6 || isSubmitting || isVerifyingInsurance || isCreatingInsurance}
        />
      )}
    </main>
  );
}
