"use client"

import { useEffect, useRef } from "react"
import Image from "next/image"
import { Button } from "@/components/ui/button"
import { X } from "lucide-react"
import { Therapist } from "@/lib/api"
import { SpecialtyBadge } from "@/components/ui/specialty-badge"

interface TherapistProfileModalProps {
  therapist: Therapist
  isOpen: boolean
  onClose: () => void
  onScheduleClick: () => void
}

export function TherapistProfileModal({ therapist, isOpen, onClose, onScheduleClick }: TherapistProfileModalProps) {
  const modalRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    // Prevent body scrolling when modal is open
    if (isOpen) {
      document.body.style.overflow = "hidden"
    } else {
      document.body.style.overflow = "auto"
    }

    return () => {
      document.body.style.overflow = "auto"
    }
  }, [isOpen])

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 z-50 bg-black bg-opacity-50 flex items-end sm:items-center justify-center">
      <div
        ref={modalRef}
        className="bg-white w-full max-h-[90vh] rounded-t-xl sm:rounded-xl sm:max-w-md overflow-hidden flex flex-col animate-in slide-in-from-bottom sm:animate-in sm:zoom-in sm:duration-300"
      >
        <div className="p-4 border-b border-gray-200 flex items-center justify-between">
          <h3 className="text-lg font-semibold">Therapist Profile</h3>
          <Button variant="ghost" size="icon" onClick={onClose} className="h-8 w-8">
            <X size={20} />
            <span className="sr-only">Close</span>
          </Button>
        </div>

        <div className="overflow-y-auto p-4 flex-1">
          <div className="flex flex-col items-center mb-6">
            <div className="relative w-40 h-40 rounded-full overflow-hidden mb-4">
              <Image src={therapist.image || "/placeholder.svg"} alt={therapist.name} fill className="object-cover" />
            </div>

            <div className="text-center">
              <h2 className="text-xl font-semibold">{therapist.name}</h2>
              <p className="text-gray-600">{therapist.title}</p>
              <p className="text-sm text-gray-600 mt-1">Licensed in: {therapist.licensedStates.join(", ")}</p>
            </div>
          </div>

          <div className="mb-6">
            <h4 className="font-medium mb-3 text-gray-700">Specialties</h4>
            <div className="flex flex-wrap gap-2">
              {therapist.specialties.map((specialty, index) => (
                <SpecialtyBadge
                  key={index}
                  text={specialty}
                  size="md"
                  variant={index % 3 === 0 ? "primary" : index % 3 === 1 ? "secondary" : "outline"}
                />
              ))}
            </div>
          </div>

          <div className="mb-4">
            <h4 className="font-medium mb-2 text-gray-700">About</h4>
            <p className="text-gray-700">{therapist.bio}</p>
          </div>

          {/* TODO: Uncomment when video functionality is implemented */}
          {/* <Button
            variant="outline"
            className="w-full flex items-center justify-center gap-2 text-[#64748B] border-[#64748B]"
          >
            <PlayCircle size={18} />
            Watch Introduction Video
          </Button> */}
        </div>

        <div className="p-4 border-t border-gray-200">
          <Button onClick={onScheduleClick} className="w-full bg-[#F7903D] hover:bg-[#e67f2d] text-white h-12">
            Schedule with {therapist.name.split(" ")[0]}
          </Button>
        </div>
      </div>
    </div>
  )
}
