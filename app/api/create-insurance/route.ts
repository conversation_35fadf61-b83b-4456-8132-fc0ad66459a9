import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    console.log('[API_CREATE_INSURANCE] =========================');
    console.log('[API_CREATE_INSURANCE] Starting insurance creation API call');
    
    const body = await request.json();
    console.log('[API_CREATE_INSURANCE] Request body received:', body);
    
    const { clientId, insuranceCompanyId, subscriber } = body;
    
    // Validate required fields
    if (!clientId) {
      console.log('[API_CREATE_INSURANCE] ❌ Missing clientId');
      return NextResponse.json(
        { success: false, error: 'clientId is required' },
        { status: 400 }
      );
    }
    
    if (!insuranceCompanyId) {
      console.log('[API_CREATE_INSURANCE] ❌ Missing insuranceCompanyId');
      return NextResponse.json(
        { success: false, error: 'insuranceCompanyId is required' },
        { status: 400 }
      );
    }
    
    if (!subscriber || !subscriber.memberId || !subscriber.firstName || !subscriber.lastName) {
      console.log('[API_CREATE_INSURANCE] ❌ Missing subscriber information');
      return NextResponse.json(
        { success: false, error: 'Complete subscriber information is required' },
        { status: 400 }
      );
    }
    
    // Prepare payload for Lavni API
    const insurancePayload = {
      clientId,
      insuranceCompanyId,
      subscriber: {
        memberId: subscriber.memberId,
        firstName: subscriber.firstName,
        lastName: subscriber.lastName,
        address: {
          state: subscriber.address?.state || 'North Carolina'
        }
      },
      deletingInsuranceCardFrontId: "none",
      deletingInsuranceCardBackId: "none"
    };

    console.log('[API_CREATE_INSURANCE] =========================');
    console.log('[API_CREATE_INSURANCE] Payload for Lavni API:');
    console.log('[API_CREATE_INSURANCE] - clientId:', insurancePayload.clientId);
    console.log('[API_CREATE_INSURANCE] - insuranceCompanyId:', insurancePayload.insuranceCompanyId);
    console.log('[API_CREATE_INSURANCE] - subscriber:', insurancePayload.subscriber);
    console.log('[API_CREATE_INSURANCE] Full payload:', JSON.stringify(insurancePayload, null, 2));

    // Call Lavni API
    console.log('[API_CREATE_INSURANCE] 🚀 Calling Lavni insurance creation API...');
    const lavniResponse = await fetch('https://api.lavnihealth.com/api/auth/create/insurance', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(insurancePayload)
    });

    console.log('[API_CREATE_INSURANCE] Lavni API response status:', lavniResponse.status);
    console.log('[API_CREATE_INSURANCE] Lavni API response headers:', Object.fromEntries(lavniResponse.headers.entries()));

    const lavniResult = await lavniResponse.json();
    console.log('[API_CREATE_INSURANCE] =========================');
    console.log('[API_CREATE_INSURANCE] Lavni API response body:', lavniResult);

    if (lavniResponse.ok && lavniResult.success) {
      console.log('[API_CREATE_INSURANCE] ✅ Insurance creation successful!');
      console.log('[API_CREATE_INSURANCE] Success details:', lavniResult);
      
      return NextResponse.json({
        success: true,
        message: 'Insurance record created successfully',
        data: lavniResult
      });
    } else {
      console.log('[API_CREATE_INSURANCE] ❌ Insurance creation failed');
      console.log('[API_CREATE_INSURANCE] Error:', lavniResult.error || 'Unknown error');
      console.log('[API_CREATE_INSURANCE] Error code:', lavniResult.errorCode);
      
      // Enhanced error logging for debugging
      if (lavniResult.error && lavniResult.error.includes('Invalid/Missing Subscriber/Insured ID')) {
        console.log('[API_CREATE_INSURANCE] 🔍 MEMBER ID DEBUG:');
        console.log('[API_CREATE_INSURANCE] - Provided memberId:', subscriber.memberId);
        console.log('[API_CREATE_INSURANCE] - memberId type:', typeof subscriber.memberId);
        console.log('[API_CREATE_INSURANCE] - memberId length:', subscriber.memberId?.length);
        console.log('[API_CREATE_INSURANCE] - memberId value (raw):', JSON.stringify(subscriber.memberId));
      }
      
      return NextResponse.json({
        success: false,
        error: lavniResult.error || 'Insurance creation failed',
        errorCode: lavniResult.errorCode,
        details: lavniResult
      }, { status: 400 });
    }

  } catch (error) {
    console.error('[API_CREATE_INSURANCE] ❌ Exception occurred:', error);
    console.error('[API_CREATE_INSURANCE] Error stack:', error instanceof Error ? error.stack : 'No stack trace');
    
    return NextResponse.json({
      success: false,
      error: 'Internal server error during insurance creation',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
} 