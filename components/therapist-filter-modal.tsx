"use client"

import React from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>er, <PERSON><PERSON>Title } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Checkbox } from "@/components/ui/checkbox"
import { Label } from "@/components/ui/label"

interface TherapistFilterProps {
  isOpen: boolean
  onClose: () => void
  filters: {
    gender: string[]
    ethnicity: string[]
  }
  onFiltersChange: (filters: {
    gender: string[]
    ethnicity: string[]
  }) => void
}

export function TherapistFilterModal({ isOpen, onClose, filters, onFiltersChange }: TherapistFilterProps) {
  const handleGenderChange = (gender: string) => {
    const currentGenders = filters?.gender || []
    const updatedGenders = currentGenders.includes(gender)
      ? currentGenders.filter((g) => g !== gender)
      : [...currentGenders, gender]
    
    onFiltersChange({
      ...filters,
      gender: updatedGenders,
      ethnicity: filters?.ethnicity || [],
    })
  }

  const handleEthnicityChange = (ethnicity: string) => {
    const currentEthnicities = filters?.ethnicity || []
    const updatedEthnicities = currentEthnicities.includes(ethnicity)
      ? currentEthnicities.filter((e) => e !== ethnicity)
      : [...currentEthnicities, ethnicity]
    
    onFiltersChange({
      ...filters,
      gender: filters?.gender || [],
      ethnicity: updatedEthnicities,
    })
  }

  const clearAllFilters = () => {
    onFiltersChange({
      gender: [],
      ethnicity: [],
    })
  }

  const hasActiveFilters = (filters?.gender?.length || 0) > 0 || (filters?.ethnicity?.length || 0) > 0

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-md bg-white">
        <DialogHeader>
          <DialogTitle>Filter Therapists</DialogTitle>
        </DialogHeader>
        
        <div className="space-y-6">
          {/* Gender Filter */}
          <div>
            <h4 className="text-base font-medium mb-3">Gender</h4>
            <div className="space-y-2">
              {["Male", "Female", "Non-binary", "Other"].map((gender) => (
                <div key={gender} className="flex items-center space-x-2">
                  <Checkbox
                    id={`gender-${gender.toLowerCase()}`}
                    checked={filters?.gender?.includes(gender) || false}
                    onCheckedChange={() => handleGenderChange(gender)}
                  />
                  <Label htmlFor={`gender-${gender.toLowerCase()}`}>{gender}</Label>
                </div>
              ))}
            </div>
          </div>

          {/* Ethnicity Filter */}
          <div>
            <h4 className="text-base font-medium mb-3">Ethnicity</h4>
            <div className="space-y-2">
              {[
                "Asian",
                "Black/African American",
                "Hispanic/Latino",
                "Middle Eastern",
                "Multiracial",
                "Native American",
                "Pacific Islander",
                "White/Caucasian",
              ].map((ethnicity) => (
                <div key={ethnicity} className="flex items-center space-x-2">
                  <Checkbox
                    id={`ethnicity-${ethnicity.replace(/\s+/g, "-").toLowerCase()}`}
                    checked={filters?.ethnicity?.includes(ethnicity) || false}
                    onCheckedChange={() => handleEthnicityChange(ethnicity)}
                  />
                  <Label htmlFor={`ethnicity-${ethnicity.replace(/\s+/g, "-").toLowerCase()}`}>
                    {ethnicity}
                  </Label>
                </div>
              ))}
            </div>
          </div>
        </div>

        <div className="flex gap-2 mt-6">
          <Button variant="outline" onClick={clearAllFilters} className="flex-1">
            Clear All
          </Button>
          <Button onClick={onClose} className="flex-1">
            Apply Filters {hasActiveFilters && `(${(filters?.gender?.length || 0) + (filters?.ethnicity?.length || 0)})`}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  )
}
