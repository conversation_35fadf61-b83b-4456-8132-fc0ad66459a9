"use client"

import * as React from "react"
import { ChevronDown, X } from "lucide-react"
import { cn } from "@/lib/utils"

interface MultiSelectProps {
  options: string[]
  value: string[]
  onChange: (value: string[]) => void
  placeholder?: string
  className?: string
  disabled?: boolean
}

export function MultiSelect({
  options,
  value,
  onChange,
  placeholder = "Select options...",
  className,
  disabled = false
}: MultiSelectProps) {
  const [isOpen, setIsOpen] = React.useState(false)
  const dropdownRef = React.useRef<HTMLDivElement>(null)

  // Close dropdown when clicking outside
  React.useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false)
      }
    }

    document.addEventListener("mousedown", handleClickOutside)
    return () => document.removeEventListener("mousedown", handleClickOutside)
  }, [])

  const handleOptionClick = (option: string) => {
    if (value.includes(option)) {
      onChange(value.filter(v => v !== option))
    } else {
      onChange([...value, option])
    }
    setIsOpen(false)
  }

  const handleRemoveTag = (option: string, e: React.MouseEvent) => {
    e.stopPropagation()
    onChange(value.filter(v => v !== option))
  }

  const availableOptions = options.filter(option => !value.includes(option))

  return (
    <div className="relative" ref={dropdownRef}>
      <div
        className={cn(
          "flex min-h-[3.5rem] w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",
          className
        )}
        onClick={() => !disabled && setIsOpen(!isOpen)}
      >
        <div className="flex flex-wrap gap-1 flex-1">
          {value.length > 0 ? (
            <>
              {value.map((item) => (
                <div
                  key={item}
                  className="flex items-center gap-1 bg-[#F7903D] text-white px-2 py-1 rounded-md text-sm"
                >
                  <span>{item}</span>
                  <button
                    type="button"
                    onClick={(e) => handleRemoveTag(item, e)}
                    className="ml-1 hover:bg-[#e67f2d] rounded p-0.5 cursor-pointer flex items-center justify-center w-4 h-4"
                    disabled={disabled}
                  >
                    <X size={12} />
                  </button>
                </div>
              ))}
              {availableOptions.length > 0 && (
                <span className="text-muted-foreground text-sm self-center ml-2">
                  Click to add more
                </span>
              )}
            </>
          ) : (
            <span className="text-muted-foreground">{placeholder}</span>
          )}
        </div>
        <ChevronDown
          className={cn(
            "h-4 w-4 opacity-50 transition-transform",
            isOpen && "rotate-180"
          )}
        />
      </div>

      {isOpen && availableOptions.length > 0 && (
        <div className="absolute z-[9999] w-full mt-1 bg-white border border-gray-200 rounded-md shadow-lg max-h-60 overflow-auto">
          {availableOptions.map((option) => (
            <div
              key={option}
              className="px-3 py-2 text-sm cursor-pointer hover:bg-gray-100 text-gray-900"
              onClick={() => handleOptionClick(option)}
            >
              {option}
            </div>
          ))}
        </div>
      )}
    </div>
  )
}
