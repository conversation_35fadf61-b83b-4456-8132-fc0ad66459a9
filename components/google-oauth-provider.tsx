"use client"

import { GoogleOAuthProvider } from '@react-oauth/google';
import { ReactNode } from 'react';

interface GoogleOAuthWrapperProps {
  children: ReactNode;
}

export function GoogleOAuthWrapper({ children }: GoogleOAuthWrapperProps) {
  const clientId = process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID;

  if (!clientId) {
    console.error('Google Client ID not found in environment variables');
    return <>{children}</>;
  }

  const GoogleOAuthProviderComponent = GoogleOAuthProvider as any;

  return (
    <GoogleOAuthProviderComponent clientId={clientId}>
      {children}
    </GoogleOAuthProviderComponent>
  );
} 