import fetch from 'node-fetch';

// Example insurance data (replace with real test data as needed)
const testData = {
  ins_name_f: '<PERSON>',
  ins_name_l: '<PERSON>',
  tradingPartnerServiceId: '68069',
  ins_dob: '19890326',
  pat_rel: '18',
  fdos: '20250228',
  ins_sex: 'F',
  prov_npi: process.env.PROVIDER_NPI,
  prov_taxid: process.env.PROVIDER_TAX_ID,
  ins_number: '900756197T',
};

async function testEligibility() {
  try {
    // Use environment variable or default to localhost
    const frontendUrl = process.env.FRONTEND_URL || 'http://localhost:3000';
    const res = await fetch(`${frontendUrl}/api/check-eligibility`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(testData),
    });
    const data = await res.json();
    console.log('Eligibility API response:', data);
    // Example: Save to localStorage (browser context only)
    // localStorage.setItem('eligibilityResult', JSON.stringify(data));
  } catch (error) {
    console.error('Test script error:', error);
  }
}

testEligibility(); 