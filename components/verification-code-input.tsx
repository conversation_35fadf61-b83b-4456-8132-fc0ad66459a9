"use client"

import type React from "react"

import { useRef, useState, useEffect } from "react"
import { Input } from "@/components/ui/input"

interface VerificationCodeInputProps {
  value: string
  onChange: (value: string) => void
  length?: number
  disabled?: boolean
}

export function VerificationCodeInput({ value, onChange, length = 6, disabled = false }: VerificationCodeInputProps) {
  const [inputValues, setInputValues] = useState<string[]>(Array(length).fill(""))
  const inputRefs = useRef<(HTMLInputElement | null)[]>([])

  // Initialize refs array
  useEffect(() => {
    inputRefs.current = inputRefs.current.slice(0, length)
  }, [length])

  // Update input values when value prop changes
  useEffect(() => {
    const valueArray = value.split("")
    setInputValues(
      Array(length)
        .fill("")
        .map((_, index) => valueArray[index] || ""),
    )
  }, [value, length])

  const handleChange = (index: number, e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value

    // Only allow numbers
    if (newValue && !/^\d+$/.test(newValue)) {
      return
    }

    // Handle paste (when multiple characters are entered at once)
    if (newValue.length > 1) {
      const pastedValue = newValue.replace(/\D/g, '').slice(0, length) // Clean and limit to length
      const newInputValues = Array(length)
        .fill("")
        .map((_, i) => pastedValue[i] || "")
      setInputValues(newInputValues)
      onChange(pastedValue)

      // Focus last filled input or the next empty one
      const focusIndex = Math.min(pastedValue.length, length - 1)
      inputRefs.current[focusIndex]?.focus()
      return
    }

    // Update the value at the current index
    const newInputValues = [...inputValues]
    newInputValues[index] = newValue
    setInputValues(newInputValues)

    // Emit the combined value
    onChange(newInputValues.join(""))

    // Auto-focus next input if value is entered
    if (newValue && index < length - 1) {
      inputRefs.current[index + 1]?.focus()
    }
  }

  // Enhanced paste handler to ensure cross-browser compatibility
  const handlePaste = (index: number, e: React.ClipboardEvent<HTMLInputElement>) => {
    e.preventDefault()
    
    const pastedData = e.clipboardData.getData('text')
    const cleanedData = pastedData.replace(/\D/g, '').slice(0, length) // Remove non-digits and limit
    
    if (cleanedData) {
      const newInputValues = Array(length)
        .fill("")
        .map((_, i) => cleanedData[i] || "")
      setInputValues(newInputValues)
      onChange(cleanedData)

      // Focus the last filled input or the next empty one
      const focusIndex = Math.min(cleanedData.length, length - 1)
      inputRefs.current[focusIndex]?.focus()
    }
  }

  const handleKeyDown = (index: number, e: React.KeyboardEvent<HTMLInputElement>) => {
    // Move focus to previous input on backspace if current input is empty
    if (e.key === "Backspace" && !inputValues[index] && index > 0) {
      inputRefs.current[index - 1]?.focus()
    }

    // Move focus with arrow keys
    if (e.key === "ArrowLeft" && index > 0) {
      e.preventDefault()
      inputRefs.current[index - 1]?.focus()
    }

    if (e.key === "ArrowRight" && index < length - 1) {
      e.preventDefault()
      inputRefs.current[index + 1]?.focus()
    }
  }

  return (
    <div className="flex justify-center gap-2">
      {Array(length)
        .fill(0)
        .map((_, index) => (
          <Input
            key={index}
            ref={(el) => {
              inputRefs.current[index] = el;
            }}
            type="text"
            inputMode="numeric"
            pattern="[0-9]*"
            maxLength={1}
            value={inputValues[index]}
            onChange={(e) => handleChange(index, e)}
            onKeyDown={(e) => handleKeyDown(index, e)}
            onPaste={(e) => handlePaste(index, e)}
            className="w-12 h-14 text-center text-xl font-medium"
            disabled={disabled}
            aria-label={`Verification code digit ${index + 1}`}
          />
        ))}
    </div>
  )
}
