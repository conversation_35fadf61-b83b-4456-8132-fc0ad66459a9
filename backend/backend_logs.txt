
> lavni-backend@1.0.0 dev
> nodemon src/index.ts

[33m[nodemon] 3.1.10[39m
[33m[nodemon] to restart at any time, enter `rs`[39m
[33m[nodemon] watching path(s): *.*[39m
[33m[nodemon] watching extensions: ts,json[39m
[32m[nodemon] starting `ts-node src/index.ts`[39m
Error: listen EADDRINUSE: address already in use :::3003
    at Server.setupListenHandle [as _listen2] (node:net:1939:16)
    at listenInCluster (node:net:1996:12)
    at Server.listen (node:net:2101:7)
    at Function.listen (/Users/<USER>/Downloads/lavni-lavni-react-65ed8d6a3a2a/public & clients/onboarding_new_client/backend/node_modules/express/lib/application.js:635:24)
    at Object.<anonymous> (/Users/<USER>/Downloads/lavni-lavni-react-65ed8d6a3a2a/public & clients/onboarding_new_client/backend/src/index.ts:6:5)
    at Module._compile (node:internal/modules/cjs/loader:1734:14)
    at Module.m._compile (/Users/<USER>/Downloads/lavni-lavni-react-65ed8d6a3a2a/public & clients/onboarding_new_client/backend/node_modules/ts-node/src/index.ts:1618:23)
    at loadTS (node:internal/modules/cjs/loader:1826:10)
    at Object.require.extensions.<computed> [as .ts] (/Users/<USER>/Downloads/lavni-lavni-react-65ed8d6a3a2a/public & clients/onboarding_new_client/backend/node_modules/ts-node/src/index.ts:1621:12)
    at Module.load (node:internal/modules/cjs/loader:1469:32) {
  code: 'EADDRINUSE',
  errno: -48,
  syscall: 'listen',
  address: '::',
  port: 3003
}
[31m[nodemon] app crashed - waiting for file changes before starting...[39m
[32m[nodemon] restarting due to changes...[39m
[32m[nodemon] starting `ts-node src/index.ts`[39m
[32m[nodemon] restarting due to changes...[39m
[32m[nodemon] starting `ts-node src/index.ts`[39m
Server is running on port 3003
Environment: development
Connected to MongoDB
[32m[nodemon] restarting due to changes...[39m
[32m[nodemon] starting `ts-node src/index.ts`[39m
Server is running on port 3003
Environment: development
Connected to MongoDB
