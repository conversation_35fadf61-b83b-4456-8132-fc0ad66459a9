"use client"

import React from 'react';
import { GoogleLogin, CredentialResponse } from '@react-oauth/google';
import { jwtDecode } from 'jwt-decode';
import { Button } from '@/components/ui/button';

interface GoogleOAuthButtonProps {
  onSuccess: (userData: any) => void;
  onError: (error: any) => void;
  clientType: 'adult' | 'minor';
  parentData?: {
    parentFirstName: string;
    parentLastName: string;
    parentEmail: string;
    parentPhone: string;
  };
  disabled?: boolean;
}

interface GoogleUserData {
  iss: string;
  azp: string;
  aud: string;
  sub: string;
  email: string;
  email_verified: boolean;
  name: string;
  picture: string;
  given_name: string;
  family_name: string;
  iat: number;
  exp: number;
}

export function GoogleOAuthButton({ 
  onSuccess, 
  onError, 
  clientType, 
  parentData,
  disabled = false 
}: GoogleOAuthButtonProps) {

  const handleGoogleSuccess = async (credentialResponse: CredentialResponse) => {
    try {
      console.log('[GOOGLE-OAUTH] Success response:', credentialResponse);

      if (!credentialResponse.credential) {
        throw new Error('No credential received from Google');
      }

      // Decode the JWT token to get user info
      const userInfo: GoogleUserData = jwtDecode(credentialResponse.credential);
      console.log('[GOOGLE-OAUTH] Decoded user info:', userInfo);

      // Build the user data object
      const userData = {
        idToken: credentialResponse.credential,
        clientId: process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID,
        email: userInfo.email,
        firstname: userInfo.given_name,
        lastname: userInfo.family_name,
        name: userInfo.name,
        picture: userInfo.picture,
        clientType,
        parentData
      };

      console.log('[GOOGLE-OAUTH] Prepared user data:', userData);

      // Call the success handler
      onSuccess(userData);

    } catch (error) {
      console.error('[GOOGLE-OAUTH] Error processing Google response:', error);
      onError(error);
    }
  };

  const handleGoogleError = () => {
    console.error('[GOOGLE-OAUTH] Google OAuth error');
    onError(new Error('Google OAuth failed'));
  };

  const GoogleLoginComponent = GoogleLogin as any;

  return (
    <div className="w-full">
      <GoogleLoginComponent
        onSuccess={handleGoogleSuccess}
        onError={handleGoogleError}
        theme="outline"
        size="large"
        width="100%"
        text="signup_with"
        shape="rectangular"
        logo_alignment="left"
        useOneTap={false}
        disabled={disabled}
      />
    </div>
  );
}

// Fallback custom button if the Google component doesn't render properly
export function CustomGoogleButton({ 
  onClick, 
  disabled = false 
}: { 
  onClick: () => void; 
  disabled?: boolean; 
}) {
  return (
    <Button
      onClick={onClick}
      variant="outline"
      className="w-full h-12 mb-6 flex items-center justify-center gap-2"
      disabled={disabled}
    >
      <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path
          d="M19.8055 10.2275C19.8055 9.51764 19.7516 8.83889 19.6347 8.18127H10.2002V11.8883H15.6016C15.3768 13.0961 14.6569 14.1242 13.5937 14.7784V17.2254H16.7542C18.6055 15.5316 19.8055 13.1128 19.8055 10.2275Z"
          fill="#4285F4"
        />
        <path
          d="M10.2002 20C12.897 20 15.1715 19.1152 16.7542 17.2254L13.5937 14.7784C12.7073 15.3784 11.5631 15.7254 10.2002 15.7254C7.5943 15.7254 5.38759 14.0117 4.58975 11.7H1.33301V14.2254C2.90817 17.6726 6.29087 20 10.2002 20Z"
          fill="#34A853"
        />
        <path
          d="M4.58978 11.7C4.39492 11.1 4.28825 10.4608 4.28825 9.8C4.28825 9.13922 4.39492 8.5 4.58978 7.9V5.37461H1.33304C0.713039 6.70922 0.366211 8.20687 0.366211 9.8C0.366211 11.3931 0.713039 12.8908 1.33304 14.2254L4.58978 11.7Z"
          fill="#FBBC05"
        />
        <path
          d="M10.2002 3.87461C11.6814 3.87461 13.0045 4.36922 14.0496 5.36843L16.8682 2.59412C15.1624 0.99098 12.8879 0 10.2002 0C6.29087 0 2.90817 2.32745 1.33301 5.77461L4.58975 8.3C5.38759 5.98824 7.5943 4.27461 10.2002 4.27461V3.87461Z"
          fill="#EA4335"
        />
      </svg>
      Sign up with Google
    </Button>
  );
} 