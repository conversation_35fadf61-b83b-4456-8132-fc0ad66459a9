import React, { useState, useEffect } from 'react';
import { 
  calculateAllAvailableDates, 
  getAvailableTimesForDate, 
  TherapistData,
  dayOfWeekAsNumber 
} from '../lib/therapist-availability';
import moment from 'moment';

interface CalendarComponentProps {
  therapistId: string;
  onDateSelect: (date: Date) => void;
  onTimeSelect: (time: string) => void;
  selectedDate?: Date;
  selectedTime?: string;
}

export const CalendarComponent: React.FC<CalendarComponentProps> = ({
  therapistId,
  onDateSelect,
  onTimeSelect,
  selectedDate,
  selectedTime
}) => {
  const [therapistData, setTherapistData] = useState<TherapistData | null>(null);
  const [availableDates, setAvailableDates] = useState<{date: Date, day: number, month: number, year: number}[]>([]);
  const [availableTimeSlots, setAvailableTimeSlots] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);
  const [currentMonth, setCurrentMonth] = useState(new Date().getMonth());
  const [currentYear, setCurrentYear] = useState(new Date().getFullYear());

  // Fetch therapist data when component mounts or therapistId changes
  useEffect(() => {
    if (!therapistId) return;
    
    const fetchTherapistData = async () => {
      setLoading(true);
      try {
        // Get current month data
        const monthStr = `${currentYear}-${(currentMonth + 1).toString().padStart(2, '0')}`;
        const response = await fetch(`/api/therapists/${therapistId}/available-days?month=${monthStr}`);
        const data = await response.json();
        
        if (response.ok) {
          setTherapistData(data);
          
          // Calculate available dates using frontend logic
          const availableDatesResult = calculateAllAvailableDates(
            data,
            currentYear,
            currentMonth,
            3 // Check 3 months ahead
          );
          
          setAvailableDates(availableDatesResult);
          
          // Auto-select first available date if none selected
          if (availableDatesResult.length > 0 && !selectedDate) {
            onDateSelect(availableDatesResult[0].date);
          }
        }
      } catch (error) {
        console.error('Error fetching therapist data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchTherapistData();
  }, [therapistId, currentMonth, currentYear]);

  // Calculate available time slots when date is selected
  useEffect(() => {
    if (!selectedDate || !therapistData) return;

    const dayOfWeek = selectedDate.getDay();
    
    // Process working hours like in your original component
    const availableHours: any[] = [];
    
    if (therapistData.workingHours?.length) {
      therapistData.workingHours.forEach((obj: any) => {
        const dayAsNumber = dayOfWeekAsNumber(obj.day);

        // Convert UTC to local time (you'll need to implement this properly)
        const startTime = obj.startTime; // Use your Util.convertUTCDateToLocalDate here
        const endTime = obj.endTime; // Use your Util.convertUTCDateToLocalDate here

        if (endTime > startTime) {
          availableHours.push({
            startTime,
            endTime,
            daysOfWeek: [dayAsNumber],
          });
        } else {
          // Handle overnight shifts
          availableHours.push({
            startTime,
            endTime: "24:00 AM",
            daysOfWeek: [dayAsNumber],
          });

          availableHours.push({
            startTime: "00:00 AM",
            endTime,
            daysOfWeek: [(dayAsNumber + 1) % 7],
          });
        }
      });
    }

    // Filter data for the selected date's month
    const selectedMonth = selectedDate.getMonth();
    const selectedYear = selectedDate.getFullYear();

    const filteredBlockedDates = therapistData.blockedDates.filter((item: any) => {
      const startMonth = moment(item.start).month();
      const startYear = moment(item.start).year();
      const endMonth = moment(item.end).month();
      const endYear = moment(item.end).year();

      return (startMonth === selectedMonth && startYear === selectedYear) ||
             (endMonth === selectedMonth && endYear === selectedYear);
    });

    const filteredAppointments = therapistData.appointments.filter((item: any) => {
      const startMonth = moment(item.start).month();
      const startYear = moment(item.start).year();
      const endMonth = moment(item.end).month();
      const endYear = moment(item.end).year();

      return (startMonth === selectedMonth && startYear === selectedYear) ||
             (endMonth === selectedMonth && endYear === selectedYear);
    });

    // Get available times using frontend logic
    const times = getAvailableTimesForDate(
      selectedDate,
      dayOfWeek,
      availableHours,
      filteredBlockedDates,
      filteredAppointments
    );

    setAvailableTimeSlots(times);
  }, [selectedDate, therapistData]);

  const renderAvailableDates = () => {
    if (loading) {
      return <div className="text-center py-4">Loading available dates...</div>;
    }

    if (!availableDates.length) {
      return <div className="text-center py-4">No available dates found</div>;
    }

    return (
      <div className="grid grid-cols-7 gap-2">
        {availableDates.slice(0, 14).map((dateInfo, index) => {
          const { date, day, month, year } = dateInfo;
          const isSelected = selectedDate && 
            selectedDate.getDate() === day &&
            selectedDate.getMonth() === month &&
            selectedDate.getFullYear() === year;

          return (
            <button
              key={`${year}-${month}-${day}`}
              onClick={() => onDateSelect(date)}
              className={`
                p-2 text-center border rounded-lg hover:bg-gray-100
                ${isSelected ? 'bg-blue-500 text-white' : 'bg-white'}
              `}
            >
              <div className="text-xs">{moment(date).format('MMM')}</div>
              <div className="font-bold">{day}</div>
              <div className="text-xs">{moment(date).format('ddd')}</div>
            </button>
          );
        })}
      </div>
    );
  };

  const renderTimeSlots = () => {
    if (!selectedDate) return null;

    if (!availableTimeSlots.length) {
      return <div className="text-center py-4">No available times for this date</div>;
    }

    return (
      <div className="grid grid-cols-2 gap-2 mt-4">
        {availableTimeSlots.map((time, index) => (
          <button
            key={index}
            onClick={() => onTimeSelect(time)}
            className={`
              p-3 text-center border rounded-lg hover:bg-gray-100
              ${selectedTime === time ? 'bg-blue-500 text-white' : 'bg-white'}
            `}
          >
            {time}
          </button>
        ))}
      </div>
    );
  };

  return (
    <div className="p-4">
      <h3 className="text-lg font-semibold mb-4">Available Dates</h3>
      {renderAvailableDates()}
      
      {selectedDate && (
        <>
          <h3 className="text-lg font-semibold mt-6 mb-4">
            Available Times for {moment(selectedDate).format('MMMM D, YYYY')}
          </h3>
          {renderTimeSlots()}
        </>
      )}
    </div>
  );
}; 