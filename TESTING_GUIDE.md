# Testing Guide for Lavni Therapy Booking System

## Overview
This guide covers testing the therapy appointment booking system with timezone handling and insurance filtering.

## Prerequisites
- Backend server running on `http://localhost:3003`
- Frontend server running on `http://localhost:3000`
- Test data populated in the database

## Automated Testing

### Quick Test Script
Run the automated test script:
```bash
./test-calendar.sh
```

This tests:
- ✅ Backend connectivity
- ✅ Frontend connectivity  
- ✅ Therapist availability API
- ✅ Insurance filtering
- ✅ Calendar page accessibility
- ✅ Therapists page accessibility

## Manual Testing Scenarios

### 1. Therapist Selection with Insurance Filtering

**Test Steps:**
1. Navigate to `http://localhost:3000`
2. Complete onboarding form with:
   - State: "North Carolina"
   - Insurance: Select any insurance option
3. Click "Find Therapists"
4. Verify therapists are filtered by state and insurance

**Expected Results:**
- Only therapists in North Carolina are shown
- Therapists with matching insurance are prioritized
- Insurance companies are displayed on therapist cards

### 2. Calendar Functionality with Timezone Offset

**Test Steps:**
1. Select a therapist from the list
2. Click "Schedule" to go to calendar page
3. Observe the calendar display
4. Select an available date
5. Check available time slots

**Expected Results:**
- Calendar shows available dates highlighted
- Time slots are displayed with 4-hour offset (times appear 4 hours earlier)
- Past dates are disabled
- Blocked dates are not selectable

### 3. Time Slot Selection

**Test Steps:**
1. On calendar page, select an available date
2. View available time slots
3. Select a time slot
4. Click "Confirm Appointment"

**Expected Results:**
- Time slots are properly formatted (e.g., "2:30 PM")
- Selected time slot is highlighted
- Confirmation button is enabled only when both date and time are selected

### 4. Frontend vs Backend Logic

**Test the migration from backend to frontend logic:**
1. Check browser console for debug logs
2. Verify frontend calculations match expected availability
3. Compare with backend logs for consistency

**Expected Results:**
- Frontend logs show proper date/time calculations
- Available slots match therapist working hours
- Appointments and blocked dates are properly excluded

## API Testing

### Test Therapist Availability
```bash
curl "http://localhost:3000/api/therapists/675c597705d202e19eab9d06/available-days?month=2025-05"
```

### Test Insurance Filtering
```bash
curl "http://localhost:3003/api/users/therapists?state=North+Carolina&insurance=INSURANCE_ID"
```

## Known Issues and Fixes

### 1. Missing mobile-calendar-selector.tsx
**Issue:** Compilation error about missing file
**Fix:** ✅ File exists and is properly exported

### 2. API Route Missing
**Issue:** Frontend trying to call non-existent API routes
**Fix:** ✅ Created proxy routes in `app/api/therapists/[id]/`

### 3. Timezone Handling
**Issue:** Times need 4-hour offset for display
**Fix:** ✅ Implemented in `convertUTCDateToLocalDate()` function

## Performance Testing

### Load Testing
- Test with multiple therapists (currently 8 therapists)
- Test calendar with multiple months of data
- Verify API response times are acceptable

### Browser Testing
- Test in Chrome, Firefox, Safari
- Test mobile responsiveness
- Verify calendar interactions work on touch devices

## Debug Information

### Frontend Logs
Check browser console for:
- `[FRONTEND]` prefixed logs for calendar calculations
- `[DEBUG]` prefixed logs for time slot processing
- API request/response logs

### Backend Logs
Check terminal for:
- `[BACKEND]` prefixed logs for data processing
- Database query logs
- Availability calculation logs

## Test Data

### Sample Therapist IDs
- Beth Philipsen: `675c597705d202e19eab9d06`
- Raymond Benton: `665e1fad12855f42904e2fa0`
- Carmen Armour: `67380de7109d25b2d746f608`

### Sample Test Scenarios
1. **Peak Hours:** Test during therapist's busiest working hours
2. **Edge Cases:** Test overnight shifts, weekend availability
3. **Blocked Periods:** Test around blocked dates and holidays
4. **Multiple Appointments:** Test when therapist has back-to-back appointments

## Troubleshooting

### Common Issues
1. **Calendar not loading:** Check API routes are working
2. **No available times:** Verify therapist has working hours set
3. **Wrong timezone:** Check offset calculation in `convertUTCDateToLocalDate()`
4. **Insurance filtering not working:** Verify insurance IDs match database

### Debug Commands
```bash
# Check if servers are running
ps aux | grep "next dev"
ps aux | grep "node.*3003"

# Test API endpoints
curl http://localhost:3000/api/therapists/ID/available-days?month=2025-05
curl http://localhost:3003/api/users/therapists

# Check logs
tail -f backend/logs/app.log  # if logging to file
```

## Success Criteria

✅ **All automated tests pass**
✅ **Calendar displays correctly with timezone offset**
✅ **Insurance filtering works**
✅ **Time slots are properly calculated**
✅ **User can complete full booking flow**
✅ **No console errors**
✅ **Mobile responsive design works**

## Next Steps

1. Add unit tests for calendar utility functions
2. Add integration tests for booking flow
3. Add error handling tests
4. Performance optimization testing
5. Accessibility testing 