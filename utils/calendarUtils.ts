import moment from "moment";

/** "Monday" → 1 … "Sunday" → 0 (same convention as Date.getDay). */
export const dayOfWeekAsNumber = (day: string): number => {
  const map: Record<string, number> = {
    Sunday: 0, Monday: 1, Tuesday: 2, Wednesday: 3,
    Thursday: 4, Friday: 5, Saturday: 6
  };
  return map[day] ?? 0;
};

/** Number of days in the month (year = 4-digit, month = 0-11). */
export const getDaysInMonth = (year: number, month: number) =>
  new Date(year, month + 1, 0).getDate();

/** Converts a malformed time string to proper "HH:mm" format. */
export const convertUTCDateToLocalDate = (timeStr: string): string => {
  try {
    // Handle malformed formats like "16:00 PM", "23:30 PM", "12:30 PM"
    const match = timeStr.match(/(\d{1,2}):(\d{2})\s*(AM|PM)?/i);
    if (!match) {
      console.warn('[TIME PARSING] Invalid time format:', timeStr);
      return "00:00";
    }

    let hour = parseInt(match[1]);
    const minute = parseInt(match[2]);
    const period = match[3]?.toUpperCase();

    // Fix malformed times
    if (hour >= 12 && period === "PM") {
      // Cases like "16:00 PM" or "23:30 PM" - hour is already in 24-hour format
      // Just use the hour as-is (ignore the PM)
    } else if (hour < 12 && period === "PM") {
      // Valid PM case like "11:30 PM"
      hour += 12;
    } else if (hour === 12 && period === "AM") {
      // 12 AM = 0 hours
      hour = 0;
    }
    // For AM cases or no period, use hour as-is

    // Ensure hour is within valid range
    hour = Math.max(0, Math.min(23, hour));

    return `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;
  } catch (error) {
    console.error('[TIME PARSING] Error parsing time:', timeStr, error);
    return "00:00";
  }
};

/** Convert UTC time string to local timezone time string */
export const convertUTCTimeToLocalTime = (timeStr: string): string => {
  try {
    // First clean the time format using existing function
    const cleanTime = convertUTCDateToLocalDate(timeStr);

    // Create a moment object assuming this time is in UTC
    // Use today's date as base, but we only care about the time
    const utcTime = moment.utc(cleanTime, 'HH:mm');

    // Convert to local timezone
    const localTime = utcTime.local();

    console.log(`[TIMEZONE] Converting ${timeStr} → ${cleanTime} (UTC) → ${localTime.format('HH:mm')} (Local)`);

    return localTime.format('HH:mm');
  } catch (error) {
    console.error('[TIMEZONE] Error converting UTC to local time:', timeStr, error);
    return convertUTCDateToLocalDate(timeStr); // Fallback to original function
  }
};

/** True if probe lies inside the half-open interval [start, end). */
export const isDateInBlockedRange = (
  start: moment.Moment,
  end: moment.Moment,
  probe: moment.Moment
) => {
  // Normalize all timestamps to remove milliseconds for accurate comparison
  const normalizedStart = start.clone().milliseconds(0);
  const normalizedEnd = end.clone().milliseconds(0);
  const normalizedProbe = probe.clone().milliseconds(0);

  return normalizedProbe.isSameOrAfter(normalizedStart) && normalizedProbe.isBefore(normalizedEnd);
};