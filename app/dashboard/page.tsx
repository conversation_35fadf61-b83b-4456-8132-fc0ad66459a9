"use client"

import { useEffect, useState } from "react"
import { useRouter } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { CheckCircle, Calendar, User, Phone } from "lucide-react"

export default function DashboardPage() {
  const router = useRouter()
  const [userData, setUserData] = useState<any>(null)
  const [appointmentData, setAppointmentData] = useState<any>(null)
  const [selectedTherapist, setSelectedTherapist] = useState<any>(null)

  useEffect(() => {
    // Check if user is logged in
    const authToken = localStorage.getItem("authToken")
    const currentUser = localStorage.getItem("currentUser")
    
    if (!authToken || !currentUser) {
      router.push("/login")
      return
    }

    // Load user data
    const storedUserData = localStorage.getItem("userData")
    const storedAppointment = localStorage.getItem("appointmentDetails")
    const storedTherapist = localStorage.getItem("selectedTherapist")
    
    if (storedUserData) setUserData(JSON.parse(storedUserData))
    if (storedAppointment) setAppointmentData(JSON.parse(storedAppointment))
    if (storedTherapist) setSelectedTherapist(JSON.parse(storedTherapist))
  }, [router])

  const handleLogout = () => {
    // Clear all stored data
    localStorage.removeItem("authToken")
    localStorage.removeItem("currentUser")
    localStorage.removeItem("userData")
    localStorage.removeItem("registeredUser")
    localStorage.removeItem("password")
    localStorage.removeItem("appointmentDetails")
    localStorage.removeItem("selectedTherapist")
    localStorage.removeItem("onboardingData")
    localStorage.removeItem("eligibilityResult")
    
    router.push("/")
  }

  const formatAppointmentDate = () => {
    if (!appointmentData?.date || !appointmentData?.timeSlot) {
      return "No appointment scheduled"
    }
    const date = new Date(appointmentData.date)
    let timeString = appointmentData.timeSlot
    if (!isNaN(Date.parse(timeString))) {
      const time = new Date(timeString)
      timeString = time.toLocaleTimeString([], { hour: 'numeric', minute: '2-digit', hour12: true })
    }
    return `${date.toLocaleDateString()} at ${timeString}`
  }

  if (!userData) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"></div>
          <p className="mt-2 text-gray-600">Loading...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <h1 className="text-xl font-semibold text-gray-900">Lavni Dashboard</h1>
            </div>
            <Button variant="outline" onClick={handleLogout}>
              Sign Out
            </Button>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Welcome Section */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-8">
          <div className="flex items-center gap-3 mb-4">
            <CheckCircle className="text-green-500" size={24} />
            <h2 className="text-2xl font-bold text-gray-900">
              Welcome, {userData.firstName || "User"}!
            </h2>
          </div>
          <p className="text-gray-600">
            Your account has been successfully created and you're all set to begin your mental health journey with Lavni.
          </p>
        </div>

        {/* Appointment Details */}
        {selectedTherapist && (
          <div className="bg-white rounded-lg shadow-sm p-6 mb-8">
            <div className="flex items-center gap-3 mb-4">
              <Calendar className="text-blue-500" size={24} />
              <h3 className="text-xl font-semibold text-gray-900">Your Upcoming Appointment</h3>
            </div>
            
            <div className="grid md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-medium text-gray-900 mb-2">Therapist Information</h4>
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <User className="text-gray-400" size={16} />
                    <span className="text-gray-700">{selectedTherapist.name}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Phone className="text-gray-400" size={16} />
                    <span className="text-gray-700">{selectedTherapist.phone || "Contact info will be provided"}</span>
                  </div>
                </div>
              </div>
              
              <div>
                <h4 className="font-medium text-gray-900 mb-2">Appointment Details</h4>
                <p className="text-gray-700">{formatAppointmentDate()}</p>
                <p className="text-sm text-gray-500 mt-1">
                  You'll receive a confirmation email with meeting details.
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Account Information */}
        <div className="bg-white rounded-lg shadow-sm p-6">
          <h3 className="text-xl font-semibold text-gray-900 mb-4">Account Information</h3>
          <div className="grid md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-medium text-gray-900 mb-2">Personal Details</h4>
              <div className="space-y-1 text-sm">
                <p><span className="text-gray-500">Name:</span> {userData.firstName} {userData.lastName}</p>
                <p><span className="text-gray-500">Email:</span> {userData.email}</p>
                <p><span className="text-gray-500">Phone:</span> {userData.phone}</p>
              </div>
            </div>
            
            <div>
              <h4 className="font-medium text-gray-900 mb-2">Next Steps</h4>
              <div className="space-y-2 text-sm">
                <p className="text-gray-700">✓ Account created successfully</p>
                <p className="text-gray-700">✓ Therapist selected</p>
                <p className="text-gray-700">✓ Appointment scheduled</p>
                <p className="text-green-600 font-medium">You're all set!</p>
              </div>
            </div>
          </div>
        </div>

        {/* Support Section */}
        <div className="bg-blue-50 rounded-lg p-6 mt-8">
          <h3 className="text-lg font-semibold text-blue-900 mb-2">Need Help?</h3>
          <p className="text-blue-700 mb-4">
            If you have any questions or need to make changes to your appointment, please contact our support team.
          </p>
          <Button className="bg-blue-600 hover:bg-blue-700 text-white">
            Contact Support
          </Button>
        </div>
      </main>
    </div>
  )
} 