"use client"

import { useState, useEffect, useMemo } from "react"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { MultiSelect } from "@/components/ui/multi-select"
import axios from "axios"
import { toast } from "@/components/ui/use-toast"

const formSchema = z.object({
  state: z.string().min(1, { message: "Please select a state before proceeding" }),
  therapyConcern: z.array(z.string()).optional(),
  insuranceCarrier: z.string().optional(),
  insuranceObject: z.any().optional(),
})

type FormValues = z.infer<typeof formSchema>

interface GetStartedFormProps {
  onSubmit: (values: FormValues) => void
  onChange: (values: FormValues) => void
  initialValues?: FormValues
}

export function GetStartedForm({ onSubmit, onChange, initialValues }: GetStartedFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [insuranceOptions, setInsuranceOptions] = useState<{ _id: string; organizationName: string; states: string[] }[]>([]);
  const [loadingInsurance, setLoadingInsurance] = useState(true);
  const [insuranceError, setInsuranceError] = useState<string | null>(null);
  const [formError, setFormError] = useState<string | null>(null);
  const [selectedInsuranceObject, setSelectedInsuranceObject] = useState<any>(null);

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: initialValues || {
      state: "",
      therapyConcern: [],
      insuranceCarrier: "",
    },
  })

  // Extract unique states from insurance companies
  const uniqueStates = useMemo(() => {
    const allStates = insuranceOptions.flatMap(opt => opt.states || []);
    // Filter out "All" from state options since it's not a real state
    return Array.from(new Set(allStates)).filter(state => state !== "All").sort();
  }, [insuranceOptions]);

  // Filter insurance companies by selected state
  const filteredInsuranceOptions = useMemo(() => {
    if (!form.watch("state")) return [];
    // Include insurance companies that serve the selected state OR serve "All" states
    const filtered = insuranceOptions.filter(opt =>
      (opt.states || []).includes(form.watch("state")) || (opt.states || []).includes("All")
    );
    // Sort alphabetically by organization name
    return filtered.sort((a, b) => a.organizationName.localeCompare(b.organizationName));
  }, [insuranceOptions, form.watch("state")]);

  // Theo dõi thay đổi giá trị form và thông báo lên component cha
  useEffect(() => {
    const subscription = form.watch((value) => {
      onChange(value as FormValues);
    });
    
    return () => subscription.unsubscribe();
  }, [form, onChange]);

  useEffect(() => {
    const fetchInsurance = async () => {
      setLoadingInsurance(true);
      setInsuranceError(null);
      try {
        const res = await axios.get("/api/insurance-companies");
        console.log("Insurance API response:", res);
        setInsuranceOptions(res.data.data || []);
      } catch (err) {
        console.error("Insurance API error:", err);
        setInsuranceError("Could not load insurance companies");
      } finally {
        setLoadingInsurance(false);
      }
    };
    fetchInsurance();
  }, []);

  const handleSubmit = async (values: FormValues) => {
    console.log("Form handleSubmit called with values:", values);
    setIsSubmitting(true);
    setFormError(null);
    if (!values.state) {
      setFormError("Please select a state before proceeding");
      setIsSubmitting(false);
      return;
    }
    try {
      await onSubmit(values);
    } catch (error: any) {
      console.error("Form submission error:", error);
      setFormError("An error occurred while submitting the form");
      toast({
        variant: "destructive",
        title: "Submission failed",
        description: error.message || "An error occurred. Please try again.",
      });
    } finally {
      setIsSubmitting(false);
    }
  }

  const handleInsuranceChange = (value: string) => {
    form.setValue("insuranceCarrier", value);
    const found = insuranceOptions.find(opt => opt.organizationName === value);
    setSelectedInsuranceObject(found || null);
    onChange({ ...form.getValues(), insuranceCarrier: value, insuranceObject: found });
  };

  return (
    <Form {...form}>
      <form id="get-started-form" onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
        {formError && (
          <div className="text-red-500 text-sm mb-4">
            {formError}
          </div>
        )}
        <FormField
          control={form.control}
          name="state"
          render={({ field }) => (
            <FormItem>
              <FormLabel>
                State <span className="text-red-500">*</span>
              </FormLabel>
              <FormControl>
                <Select
                  onValueChange={(value) => {
                    field.onChange(value);
                    form.trigger("state");
                  }}
                  value={field.value}
                  disabled={isSubmitting || loadingInsurance}
                >
                  <SelectTrigger className={`h-14 text-base ${form.formState.errors.state ? 'border-red-500' : ''}`}>
                    <SelectValue placeholder={loadingInsurance ? "Loading..." : "Select your state"} />
                  </SelectTrigger>
                  <SelectContent>
                    {loadingInsurance && <SelectItem value="loading">Loading...</SelectItem>}
                    {insuranceError && <SelectItem value="error">{insuranceError}</SelectItem>}
                    {!loadingInsurance && !insuranceError && uniqueStates.length === 0 && (
                      <SelectItem value="no-data">No states found</SelectItem>
                    )}
                    {!loadingInsurance && uniqueStates.map((state, index) => (
                      <SelectItem key={state || `state-${index}`} value={state}>{state}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="therapyConcern"
          render={({ field }) => (
            <FormItem>
              <FormLabel>
                Therapy Concerns <span className="text-gray-500 font-normal">(optional - select all that apply)</span>
              </FormLabel>
              <FormControl>
                <MultiSelect
                  options={[
                    "Aggressive Behavior",
                    "Binging (Food)",
                    "Body Image Distortion",
                    "Burned Out",
                    "Changes in Appetite",
                    "Changes in Mood",
                    "Communication Issues",
                    "Confusion",
                    "Daytime Sleepiness",
                    "Delusions",
                    "Difficulty Concentrating",
                    "Difficulty Sleeping",
                    "Erectile Dysfunction",
                    "Excessive Worry",
                    "Fatigue",
                    "Flashbacks",
                    "Gender Discomfort",
                    "Hallucinations",
                    "Impulse Control",
                    "Intrusive Thoughts",
                    "Lack of Self-Control",
                    "Low Sex Drive",
                    "Memory Issues",
                    "Nightmares",
                    "Persistent Sadness",
                    "Repetitive Behaviors",
                    "Restlessness",
                    "Restricting (Food)",
                    "Social Difficulties",
                    "Stressed",
                    "Substance Cravings"
                  ]}
                  value={field.value || []}
                  onChange={(newValue) => {
                    field.onChange(newValue);
                    onChange({ ...form.getValues(), therapyConcern: newValue });
                  }}
                  placeholder="Select your concerns"
                  className={`text-base ${form.formState.errors.therapyConcern ? 'border-red-500' : ''}`}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="insuranceCarrier"
          render={({ field }) => (
            <FormItem>
              <FormLabel>
                Insurance Carrier <span className="text-gray-500 font-normal">(optional)</span>
              </FormLabel>
              <FormControl>
                <Select onValueChange={handleInsuranceChange} value={field.value} disabled={isSubmitting || loadingInsurance || !form.watch("state")}>
                  <SelectTrigger className={`h-14 text-base ${form.formState.errors.insuranceCarrier ? 'border-red-500' : ''}`}>
                    <SelectValue placeholder={loadingInsurance ? "Loading..." : (!form.watch("state") ? "Select a state first" : "Skip if unsure")} />
                  </SelectTrigger>
                  <SelectContent>
                    {loadingInsurance && <SelectItem value="loading">Loading...</SelectItem>}
                    {insuranceError && <SelectItem value="error">{insuranceError}</SelectItem>}
                    {!loadingInsurance && !insuranceError && form.watch("state") && filteredInsuranceOptions.length === 0 && (
                      <SelectItem value="no-data">No insurance companies found</SelectItem>
                    )}
                    {!loadingInsurance && form.watch("state") && filteredInsuranceOptions.map((opt, index) => (
                      <SelectItem key={opt._id || `insurance-${index}`} value={opt.organizationName}>{opt.organizationName}</SelectItem>
                    ))}
                    <SelectItem value="other">Other</SelectItem>
                  </SelectContent>
                </Select>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <button type="submit" style={{ display: 'none' }} />
      </form>
    </Form>
  )
}
