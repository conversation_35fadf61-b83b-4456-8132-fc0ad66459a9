import { useCallback } from 'react';
import { event } from '@/lib/gtag';

interface AnalyticsEvent {
  action: string;
  category: string;
  label?: string;
  value?: number;
}

export const useAnalytics = () => {
  const trackEvent = useCallback((eventData: AnalyticsEvent) => {
    event(eventData);
  }, []);

  // Pre-defined tracking functions cho các hành động phổ biến
  const trackButtonClick = useCallback((buttonName: string, location?: string) => {
    trackEvent({
      action: 'click',
      category: 'button',
      label: location ? `${buttonName} - ${location}` : buttonName,
    });
  }, [trackEvent]);

  const trackPageView = useCallback((pageName: string) => {
    trackEvent({
      action: 'page_view',
      category: 'navigation',
      label: pageName,
    });
  }, [trackEvent]);

  const trackFormSubmit = useCallback((formName: string, success: boolean) => {
    trackEvent({
      action: 'form_submit',
      category: 'form',
      label: `${formName} - ${success ? 'success' : 'error'}`,
    });
  }, [trackEvent]);

  const trackUserAction = useCallback((action: string, details?: string) => {
    trackEvent({
      action,
      category: 'user_interaction',
      label: details,
    });
  }, [trackEvent]);

  return {
    trackEvent,
    trackButtonClick,
    trackPageView,
    trackFormSubmit,
    trackUserAction,
  };
}; 