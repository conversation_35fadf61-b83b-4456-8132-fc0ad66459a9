import type React from "react"
import type { Metada<PERSON> } from "next"
import { Inter } from "next/font/google"
import "./globals.css"
import { ThemeProvider } from "@/components/theme-provider"
import { ThemeScript } from "@/components/theme-script"
import { GoogleOAuthWrapper } from "@/components/google-oauth-provider"
import { Toaster } from "@/components/ui/sonner"
import GoogleAnalytics from "@/components/google-analytics"
import GoogleTagManager, { GoogleTagManagerNoScript } from "@/components/google-tag-manager"

const inter = Inter({ subsets: ["latin"] })

export const metadata: Metadata = {
  title: "Lavni - Mental Health Platform",
  description: "Mental health platform for Medicaid clients",
  manifest: "/manifest.webmanifest",
  generator: 'v0.dev'
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        <ThemeScript />
      </head>
      <body className={inter.className} suppressHydrationWarning>
        <GoogleAnalytics />
        <GoogleTagManager />
        <GoogleTagManagerNoScript />
        <GoogleOAuthWrapper>
          <ThemeProvider attribute="class" defaultTheme="light" enableSystem={false} disableTransitionOnChange>
            {children}
            <Toaster position="top-center" richColors />
          </ThemeProvider>
        </GoogleOAuthWrapper>
      </body>
    </html>
  )
}
