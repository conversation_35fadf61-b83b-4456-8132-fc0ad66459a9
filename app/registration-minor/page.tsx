"use client"

import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { useState, useEffect } from "react"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"
import { StickyBottomCTA } from "@/components/sticky-bottom-cta"
import { MobileStepTracker } from "@/components/mobile-step-tracker"
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Button } from "@/components/ui/button"
import { GoogleOAuthButton } from "@/components/google-oauth-button"
import { AlertCircle } from "lucide-react"
import { toast } from "sonner"
import axios from "axios"

// Phone number formatting utility
const formatPhoneNumber = (value: string) => {
  // Remove all non-digits
  const phoneNumber = value.replace(/\D/g, '')

  // Format as (XXX) XXX-XXXX
  if (phoneNumber.length >= 6) {
    return `(${phoneNumber.slice(0, 3)}) ${phoneNumber.slice(3, 6)}-${phoneNumber.slice(6, 10)}`
  } else if (phoneNumber.length >= 3) {
    return `(${phoneNumber.slice(0, 3)}) ${phoneNumber.slice(3)}`
  } else {
    return phoneNumber
  }
}

// Form validation schema
const formSchema = z.object({
  parentFirstName: z.string().min(1, { message: "First name is required" }),
  parentLastName: z.string().min(1, { message: "Last name is required" }),
  parentEmail: z.string().email({ message: "Invalid email address" }),
  parentPhone: z.string()
    .min(1, { message: "Phone number is required" })
    .refine((phone) => {
      const digits = phone.replace(/\D/g, '')
      return digits.length === 10
    }, { message: "Please enter a valid 10-digit phone number" }),
  childFirstName: z.string().min(1, { message: "First name is required" }),
  childLastName: z.string().min(1, { message: "Last name is required" }),
  childDob: z.string().refine(
    (dob) => {
      const date = new Date(dob)
      const today = new Date()
      const age = today.getFullYear() - date.getFullYear()
      return age < 18
    },
    { message: "Child must be under 18 years old" },
  ),
  childGender: z.string().min(1, { message: "Gender is required" }),
  insurance: z.string().optional(),
  memberId: z.string().optional(),
  password: z.string().min(8, { message: "Password must be at least 8 characters" }),
  zipcode: z.string().optional(),
})

type FormValues = z.infer<typeof formSchema>

export default function RegistrationMinorPage() {
  const router = useRouter()
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [insuranceOptions, setInsuranceOptions] = useState<{ _id: string; organizationName: string; states: string[] }[]>([])
  const [insuranceLoading, setInsuranceLoading] = useState(true)
  const [insuranceError, setInsuranceError] = useState<string | null>(null)
  const [formError, setFormError] = useState<string | null>(null)
  const [apiError, setApiError] = useState(false)
  const [showGoogleForm, setShowGoogleForm] = useState(false)
  const [googleUserData, setGoogleUserData] = useState<any>(null)

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      parentFirstName: "",
      parentLastName: "",
      parentEmail: "",
      parentPhone: "",
      childFirstName: "",
      childLastName: "",
      childDob: "",
      childGender: "",
      insurance: "",
      memberId: "",
      password: "",
      zipcode: "",
    },
  })

  // Google form schema for additional info (minor specific)
  const googleFormSchema = z.object({
    parentFirstName: z.string().min(1, { message: "Parent first name is required" }),
    parentLastName: z.string().min(1, { message: "Parent last name is required" }),
    parentEmail: z.string().email({ message: "Valid parent email is required" }),
    parentPhone: z.string()
      .min(1, { message: "Phone number is required" })
      .refine((phone) => {
        const digits = phone.replace(/\D/g, '')
        return digits.length === 10
      }, { message: "Please enter a valid 10-digit phone number" }),
    childFirstName: z.string().min(1, { message: "Child first name is required" }),
    childLastName: z.string().min(1, { message: "Child last name is required" }),
    childDob: z.string()
      .min(1, { message: "Child date of birth is required" })
      .refine((date) => {
        const birthDate = new Date(date)
        const today = new Date()
        const age = today.getFullYear() - birthDate.getFullYear()
        return age < 18
      }, { message: "Child must be under 18 years old" }),
    zipcode: z.string().min(5, { message: "Valid zipcode is required" }),
  })

  type GoogleFormValues = z.infer<typeof googleFormSchema>

  const googleForm = useForm<GoogleFormValues>({
    resolver: zodResolver(googleFormSchema),
    defaultValues: {
      parentFirstName: "",
      parentLastName: "",
      parentEmail: "",
      parentPhone: "",
      childFirstName: "",
      childLastName: "",
      childDob: "",
      zipcode: "",
    },
  })

  // Add useEffect for insurance loading
  useEffect(() => {
    let onboardingData: any = null
    let selectedState = ""
    let selectedInsurance = ""
    try {
      const stored = localStorage.getItem("onboardingData")
      if (stored) {
        onboardingData = JSON.parse(stored)
        selectedState = onboardingData.state || ""
        selectedInsurance = onboardingData.insuranceCarrier || ""
      }
    } catch (e) {}

    // Check for prefilled data from adult registration
    let prefillData: any = null
    try {
      const storedPrefill = localStorage.getItem("minorRegistrationPrefill")
      if (storedPrefill) {
        prefillData = JSON.parse(storedPrefill)
        
        // Prefill parent information from adult form data
        form.setValue("parentFirstName", prefillData.firstName || "")
        form.setValue("parentLastName", prefillData.lastName || "")
        form.setValue("parentEmail", prefillData.email || "")
        form.setValue("parentPhone", prefillData.phone || "")
        form.setValue("zipcode", prefillData.zipcode || "")
        form.setValue("insurance", prefillData.insurance || "")
        form.setValue("memberId", prefillData.memberId || "")
        form.setValue("password", prefillData.password || "")
        
        // Set child's date of birth from the adult form
        form.setValue("childDob", prefillData.dob || "")
        
        // Clear the prefill data after using it
        localStorage.removeItem("minorRegistrationPrefill")
        
        // Show a success message
        toast.success("Form pre-filled with your information. Please complete the remaining fields.")
      }
    } catch (e) {
      console.error("Error loading prefill data:", e)
    }

    // Fetch insurance options from backend, filtered by state if available
    const fetchInsurance = async () => {
      setInsuranceLoading(true)
      setInsuranceError(null)
      try {
        const res = await axios.get("/api/insurance-companies")
        let options = res.data.data || []
        if (selectedState) {
          options = options.filter((opt: any) => 
            (opt.states || []).includes(selectedState) || 
            (opt.states || []).includes("All")
          )
        }
        // Sort insurance options alphabetically
        options = options.sort((a: any, b: any) => a.organizationName.localeCompare(b.organizationName))
        setInsuranceOptions(options)
        // If user has a previous selection, set it as default
        if (selectedInsurance && options.some((opt: any) => opt.organizationName === selectedInsurance)) {
          form.setValue("insurance", selectedInsurance)
        }
        // If we have prefill data, prioritize that
        if (prefillData && prefillData.insurance) {
          form.setValue("insurance", prefillData.insurance)
        }
      } catch (err) {
        setInsuranceError("Could not load insurance companies")
      } finally {
        setInsuranceLoading(false)
      }
    }
    fetchInsurance()
  }, [])

  const handleSubmit = async (values: FormValues) => {
    setIsSubmitting(true)
    setApiError(false)
    setFormError(null)
    try {
      // Format phone number
      const phoneDigitsOnly = values.parentPhone.replace(/\D/g, "")
      const formattedPhone = phoneDigitsOnly.startsWith("1") 
        ? "+" + phoneDigitsOnly 
        : "+1" + phoneDigitsOnly
      
      // Get therapist and appointment info from localStorage
      let therapistId = ""
      let appointmentStart = ""
      let appointmentEnd = ""
      
      const storedTherapist = localStorage.getItem("selectedTherapist")
      console.log("DEBUG - selectedTherapist from localStorage:", storedTherapist)
      
      if (storedTherapist) {
        try {
          const therapistData = JSON.parse(storedTherapist)
          console.log("DEBUG - Parsed therapist data:", therapistData)
          
          therapistId = therapistData._id || therapistData.id || ""
          
          if (!therapistId && therapistData.therapistId) {
            therapistId = therapistData.therapistId
          }
          
          console.log("DEBUG - Found therapistId:", therapistId)
        } catch (error) {
          console.error("Error parsing therapist data:", error)
        }
      }
      
      const storedTherapistInfo = localStorage.getItem("therapist")
      if (!therapistId && storedTherapistInfo) {
        try {
          const therapistInfo = JSON.parse(storedTherapistInfo)
          therapistId = therapistInfo._id || therapistInfo.id || ""
          console.log("DEBUG - Found therapistId from therapist item:", therapistId)
        } catch (error) {
          console.error("Error parsing therapist info:", error)
        }
      }
      
      const storedAppointment = localStorage.getItem("appointmentDetails")
      console.log("DEBUG - appointmentDetails from localStorage:", storedAppointment)
      
      if (storedAppointment) {
        try {
          const appointmentData = JSON.parse(storedAppointment)
          console.log("DEBUG - Parsed appointment data:", appointmentData)
          
          if (appointmentData.date) {
            if (appointmentData.timeSlot && !appointmentData.timeSlot.includes(" - ")) {
              const date = new Date(appointmentData.date)
              const timeParts = appointmentData.timeSlot.split(":")
              
              if (timeParts.length >= 2) {
                let hours = parseInt(timeParts[0], 10)
                const minutes = parseInt(timeParts[1], 10)
                
                const isPM = /pm/i.test(appointmentData.timeSlot)
                if (isPM && hours < 12) hours += 12
                
                const startDate = new Date(date)
                startDate.setHours(hours, minutes, 0, 0)
                
                const endDate = new Date(startDate)
                endDate.setHours(endDate.getHours() + 1)
                
                appointmentStart = startDate.toISOString()
                appointmentEnd = endDate.toISOString()
                
                console.log("DEBUG - Created appointment time from simple format:", {
                  appointmentStart,
                  appointmentEnd
                })
              }
            } 
            else if (appointmentData.timeSlot && appointmentData.timeSlot.includes(" - ")) {
              const timeSlotParts = appointmentData.timeSlot.split(" - ")
              if (timeSlotParts.length === 2) {
                const startTimeParts = timeSlotParts[0].split(":")
                const endTimeParts = timeSlotParts[1].split(":")
                
                if (startTimeParts.length >= 2 && endTimeParts.length >= 2) {
                  let startHour = parseInt(startTimeParts[0], 10)
                  let startMinute = 0
                  
                  if (startTimeParts[1].match(/^\d+/)) {
                    startMinute = parseInt(startTimeParts[1].match(/^\d+/)[0], 10)
                  }
                  
                  const isStartPM = /pm/i.test(timeSlotParts[0])
                  if (isStartPM && startHour < 12) startHour += 12
                  
                  let endHour = parseInt(endTimeParts[0], 10)
                  let endMinute = 0
                  
                  if (endTimeParts[1].match(/^\d+/)) {
                    endMinute = parseInt(endTimeParts[1].match(/^\d+/)[0], 10)
                  }
                  
                  const isEndPM = /pm/i.test(timeSlotParts[1])
                  if (isEndPM && endHour < 12) endHour += 12
                  
                  const startDate = new Date(appointmentData.date)
                  startDate.setHours(startHour, startMinute, 0, 0)
                  
                  const endDate = new Date(appointmentData.date)
                  endDate.setHours(endHour, endMinute, 0, 0)
                  
                  appointmentStart = startDate.toISOString()
                  appointmentEnd = endDate.toISOString()
                  
                  console.log("DEBUG - Created appointment time from range format:", {
                    appointmentStart,
                    appointmentEnd
                  })
                }
              }
            }
          }
        } catch (error) {
          console.error("Error parsing appointment data:", error)
        }
      }
      
      const payload = {
        userData: {
          firstname: values.childFirstName,  // child first name -> firstname
          lastname: values.childLastName,   // child last name -> lastname
          email: values.parentEmail,        // parent email -> email
          username: values.parentEmail,     // parent email -> username
          dateOfBirth: values.childDob,
          gender: values.childGender,
          primaryPhone: formattedPhone,     // parent phone -> phone
          state: "",
          zipCode: values.zipcode,
          password: values.password,
          role: "CLIENT",
          skip: false,
          PersonalizeMatchData: {},
          clientType: "MINOR",
          parent: {
            firstname: values.parentFirstName,
            lastname: values.parentLastName,
            email: values.parentEmail,
            phone: formattedPhone
          }
        },
        likeTherapist: {
          therapistId: therapistId
        },
        appointmentObj: {
          start: appointmentStart,
          end: appointmentEnd,
          title: "Initial Session",
          reminders: [],
          color: "#3788d8",
          groupId: "",
          therapistId: therapistId,
          repeatInfo: {
            repeatType: "DOES NOT REPEAT",
            interval: "1",
            repeatDays: {
              sunday: false,
              monday: false,
              tuesday: false,
              wednesday: false,
              thursday: false,
              friday: false,
              saturday: false
            },
            endingDate: "",
            endingAfter: null,
            endingType: ""
          }
        },
        referralInfo: {
          referralCode: ""
        }
      }

      localStorage.setItem("userData", JSON.stringify(values))
      localStorage.setItem("password", JSON.stringify(values.password))

      const response = await fetch("https://api.lavnihealth.com/api/public/registerWithEmail", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(payload),
      })

      const data = await response.json()

      if (data && data.data && data.data._id) {
        localStorage.setItem("registeredUser", JSON.stringify(data.data))
      }

      if (response.ok && (!data.success || data.success === undefined)) {
        if (data.error || data.message) {
          setFormError(data.error || data.message || "Registration failed. Please try again.")
          setApiError(true)
          toast.error(data.error || data.message || "Registration failed. Please try again.", {
            duration: 6000,
            position: "top-right"
          })
          console.error("API Registration Error:", data)
          window.scrollTo({ top: 0, behavior: 'smooth' })
          setIsSubmitting(false)
          return
        }
      }
      if (response.ok && (data.success === true || data.status === 'success')) {
        toast.success("Registration successful!", {
          duration: 6000,
          position: "top-right"
        })
        router.push("/verification")
      } else {
        setFormError(data.error || data.message || "Registration failed. Please try again.")
        setApiError(true)
        toast.error("An error occurred while connecting to the server. Please try again later.", {
          duration: 6000,
          position: "top-right"
        })
        console.error("API Registration Error:", data)
        window.scrollTo({ top: 0, behavior: 'smooth' })
        setIsSubmitting(false)
      }
    } catch (error) {
      setFormError("An error occurred while connecting to the server. Please try again later.")
      setApiError(true)
      console.error("Registration error:", error)
      toast.error("An error occurred while connecting to the server. Please try again later.", {
        duration: 6000,
        position: "top-right"
      })
      window.scrollTo({ top: 0, behavior: 'smooth' })
      setIsSubmitting(false)
    }
  }

  // Auto-scroll to first error field on validation error
  const onError = (errors: any) => {
    const firstErrorField = Object.keys(errors)[0]
    const el = document.querySelector(`[name='${firstErrorField}']`)
    if (el && el.scrollIntoView) {
      el.scrollIntoView({ behavior: 'smooth', block: 'center' })
      ;(el as HTMLElement).focus()
    }
  }

  const handleGoogleSignIn = async (googleUserData: any) => {
    console.log('[GOOGLE-SIGNUP-MINOR] Google sign in successful:', googleUserData);
    setGoogleUserData(googleUserData);

    // Pre-fill parent info from Google data
    googleForm.setValue("parentFirstName", googleUserData.firstname || "");
    googleForm.setValue("parentLastName", googleUserData.lastname || "");
    googleForm.setValue("parentEmail", googleUserData.email || "");

    setShowGoogleForm(true);
  };

  const handleGoogleFormSubmit = async (values: GoogleFormValues) => {
    setIsSubmitting(true);
    try {
      console.log('[GOOGLE-SIGNUP-MINOR] Starting Google registration with form data:', values);

      const onboardingData = JSON.parse(localStorage.getItem("onboardingData") || "{}");

      // Format phone number
      const phoneDigitsOnly = values.parentPhone.replace(/\D/g, "")
      const formattedPhone = phoneDigitsOnly.startsWith("1")
        ? "+" + phoneDigitsOnly
        : "+1" + phoneDigitsOnly

      // Get therapist and appointment info from localStorage (same as email registration)
      let therapistId = "";
      let appointmentStart = "";
      let appointmentEnd = "";
      
      const storedTherapist = localStorage.getItem("selectedTherapist");
      console.log("[GOOGLE-SIGNUP-MINOR] selectedTherapist from localStorage:", storedTherapist);
      
      if (storedTherapist) {
        try {
          const therapistData = JSON.parse(storedTherapist);
          console.log("[GOOGLE-SIGNUP-MINOR] Parsed therapist data:", therapistData);
          
          therapistId = therapistData._id || therapistData.id || "";
          
          if (!therapistId && therapistData.therapistId) {
            therapistId = therapistData.therapistId;
          }
          
          console.log("[GOOGLE-SIGNUP-MINOR] Found therapistId:", therapistId);
        } catch (error) {
          console.error("[GOOGLE-SIGNUP-MINOR] Error parsing therapist data:", error);
        }
      }
      
      const storedTherapistInfo = localStorage.getItem("therapist");
      if (!therapistId && storedTherapistInfo) {
        try {
          const therapistInfo = JSON.parse(storedTherapistInfo);
          therapistId = therapistInfo._id || therapistInfo.id || "";
          console.log("[GOOGLE-SIGNUP-MINOR] Found therapistId from therapist item:", therapistId);
        } catch (error) {
          console.error("[GOOGLE-SIGNUP-MINOR] Error parsing therapist info:", error);
        }
      }
      
      const storedAppointment = localStorage.getItem("appointmentDetails");
      console.log("[GOOGLE-SIGNUP-MINOR] appointmentDetails from localStorage:", storedAppointment);
      
      if (storedAppointment) {
        try {
          const appointmentData = JSON.parse(storedAppointment);
          console.log("[GOOGLE-SIGNUP-MINOR] Parsed appointment data:", appointmentData);
          
          if (appointmentData.date) {
            if (appointmentData.timeSlot && !appointmentData.timeSlot.includes(" - ")) {
              const date = new Date(appointmentData.date);
              const timeParts = appointmentData.timeSlot.split(":");
              
              if (timeParts.length >= 2) {
                let hours = parseInt(timeParts[0], 10);
                const minutes = parseInt(timeParts[1], 10);
                
                const isPM = /pm/i.test(appointmentData.timeSlot);
                if (isPM && hours < 12) hours += 12;
                
                const startDate = new Date(date);
                startDate.setHours(hours, minutes, 0, 0);
                
                const endDate = new Date(startDate);
                endDate.setHours(endDate.getHours() + 1);
                
                appointmentStart = startDate.toISOString();
                appointmentEnd = endDate.toISOString();
                
                console.log("[GOOGLE-SIGNUP-MINOR] Created appointment time from simple format:", {
                  appointmentStart,
                  appointmentEnd
                });
              }
            } 
            else if (appointmentData.timeSlot && appointmentData.timeSlot.includes(" - ")) {
              const timeSlotParts = appointmentData.timeSlot.split(" - ");
              if (timeSlotParts.length === 2) {
                const startTimeParts = timeSlotParts[0].split(":");
                const endTimeParts = timeSlotParts[1].split(":");
                
                if (startTimeParts.length >= 2 && endTimeParts.length >= 2) {
                  let startHour = parseInt(startTimeParts[0], 10);
                  let startMinute = 0;
                  
                  if (startTimeParts[1].match(/^\d+/)) {
                    startMinute = parseInt(startTimeParts[1].match(/^\d+/)[0], 10);
                  }
                  
                  const isStartPM = /pm/i.test(timeSlotParts[0]);
                  if (isStartPM && startHour < 12) startHour += 12;
                  
                  let endHour = parseInt(endTimeParts[0], 10);
                  let endMinute = 0;
                  
                  if (endTimeParts[1].match(/^\d+/)) {
                    endMinute = parseInt(endTimeParts[1].match(/^\d+/)[0], 10);
                  }
                  
                  const isEndPM = /pm/i.test(timeSlotParts[1]);
                  if (isEndPM && endHour < 12) endHour += 12;
                  
                  const startDate = new Date(appointmentData.date);
                  startDate.setHours(startHour, startMinute, 0, 0);
                  
                  const endDate = new Date(appointmentData.date);
                  endDate.setHours(endHour, endMinute, 0, 0);
                  
                  appointmentStart = startDate.toISOString();
                  appointmentEnd = endDate.toISOString();
                  
                  console.log("[GOOGLE-SIGNUP-MINOR] Created appointment time from range format:", {
                    appointmentStart,
                    appointmentEnd
                  });
                }
              }
            }
          }
        } catch (error) {
          console.error("[GOOGLE-SIGNUP-MINOR] Error parsing appointment data:", error);
        }
      }

      // Step 1: Register with Lavni Google API using parent's email
      const lavniApiUrl = process.env.NEXT_PUBLIC_LAVNI_API_URL || 'https://api.lavnihealth.com';
      const registrationPayload = {
        userData: {
          idToken: googleUserData.idToken,
          clientId: googleUserData.clientId,
          role: "CLIENT",
          primaryPhone: formattedPhone,
          firstname: values.childFirstName,
          lastname: values.childLastName,
          gender: "prefer-not-to-say", // Default for minors
          dateOfBirth: values.childDob,
          ethnicityId: "65b9bfb7ad5d1dc915d76585", // Default ethnicity ID
          username: `${values.childFirstName.toLowerCase()}_${Date.now()}`,
          state: onboardingData.state || "Unknown",
          zipCode: values.zipcode,
          skip: false
        },
        likeTherapist: {
          therapistId: therapistId
        },
        appointmentObj: {
          start: appointmentStart,
          end: appointmentEnd,
          title: "Initial Session",
          reminders: [],
          color: "#3788d8",
          groupId: "",
          therapistId: therapistId,
          repeatInfo: {
            repeatType: "DOES NOT REPEAT",
            interval: "1",
            repeatDays: {
              sunday: false,
              monday: false,
              tuesday: false,
              wednesday: false,
              thursday: false,
              friday: false,
              saturday: false
            },
            endingDate: "",
            endingAfter: null,
            endingType: ""
          }
        },
        referralInfo: {
          referralCode: ""
        }
      };

      console.log('[GOOGLE-SIGNUP-MINOR] Calling Lavni API with payload:', registrationPayload);

      const lavniResponse = await fetch(`${lavniApiUrl}/api/public/signUpWithGoogleReact`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(registrationPayload),
      });

      const lavniResult = await lavniResponse.json();
      console.log('[GOOGLE-SIGNUP-MINOR] Lavni API response:', lavniResult);

      if (!lavniResponse.ok || !lavniResult.success) {
        throw new Error(lavniResult.message || 'Failed to register with Lavni API');
      }

      // Step 2: Update user profile with parent information
      if (lavniResult.data && lavniResult.data._id) {
        console.log('[GOOGLE-SIGNUP-MINOR] Updating user profile for minor client');
        
        const profileUpdatePayload = {
          userId: lavniResult.data._id,
          clientType: 'minor',
          parent: {
            parentFirstName: values.parentFirstName,
            parentLastName: values.parentLastName,
            parentEmail: values.parentEmail,
            parentPhone: formattedPhone
          },
          additionalData: {
            medium: 'GOOGLE',
            googleId: googleUserData.email,
          }
        };

        const profileResponse = await fetch('/api/update-user-profile', {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(profileUpdatePayload),
        });

        const profileResult = await profileResponse.json();
        console.log('[GOOGLE-SIGNUP-MINOR] Profile update response:', profileResult);

        if (!profileResponse.ok) {
          console.warn('[GOOGLE-SIGNUP-MINOR] Profile update failed, but continuing:', profileResult);
        }
      }

      // Store user data
      localStorage.setItem("registeredUser", JSON.stringify(lavniResult.data));
      localStorage.setItem("userData", JSON.stringify({
        firstName: values.childFirstName,
        lastName: values.childLastName,
        email: values.parentEmail, // Use parent's email for communication
        phone: formattedPhone,
        zipcode: values.zipcode,
        registrationType: 'google',
        clientType: 'minor',
        parent: {
          parentFirstName: values.parentFirstName,
          parentLastName: values.parentLastName,
          parentEmail: values.parentEmail,
          parentPhone: formattedPhone
        }
      }));

      console.log('[GOOGLE-SIGNUP-MINOR] Google registration successful, redirecting to verification');
      toast.success("Registration successful! Please verify the parent's account.");

      // Redirect to verification page
      router.push("/verification");

    } catch (error) {
      console.error('[GOOGLE-SIGNUP-MINOR] Error:', error);
      toast.error(error instanceof Error ? error.message : "Failed to register with Google");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleGoogleError = (error: any) => {
    console.error('[GOOGLE-SIGNUP-MINOR] Google OAuth error:', error);
    toast.error("Failed to sign in with Google. Please try again.");
  };

  return (
    <main className="flex min-h-screen flex-col bg-white">
      <div className="flex-1 px-4 py-6 pb-36">
        <div className="mb-6">
          <h1 className="text-2xl font-bold text-gray-900">Create Account</h1>
          <p className="text-gray-600 mt-2">Please provide information for both guardian and minor.</p>
        </div>

        <MobileStepTracker currentStep={5} totalSteps={7} />

        {!showGoogleForm && (
          <>
            <GoogleOAuthButton
              onSuccess={handleGoogleSignIn}
              onError={handleGoogleError}
              clientType="minor"
              disabled={isSubmitting}
            />

            <div className="relative my-6">
              <div className="absolute inset-0 flex items-center">
                <div className="w-full border-t border-gray-300"></div>
              </div>
              <div className="relative flex justify-center text-sm">
                <span className="px-2 bg-white text-gray-500">or continue with email</span>
              </div>
            </div>
          </>
        )}

        {showGoogleForm && googleUserData && (
          <div className="mb-6">
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
              <h3 className="text-lg font-medium text-blue-900 mb-2">Complete Your Registration</h3>
              <p className="text-blue-700 text-sm">
                Signed in with Google as <strong>{googleUserData.email}</strong>.
                Please complete your registration by providing additional information below.
              </p>
            </div>

            <Form {...googleForm}>
              <form onSubmit={(e) => {
                console.log('[FORM-SUBMIT] Form onSubmit triggered');
                console.log('[FORM-SUBMIT] Form state:', googleForm.formState);
                console.log('[FORM-SUBMIT] Form values:', googleForm.getValues());
                return googleForm.handleSubmit(handleGoogleFormSubmit)(e);
              }} className="space-y-6">
                {formError && (
                  <div className="text-red-500 text-sm mb-4 border border-red-500 bg-red-50 rounded p-2">{formError}</div>
                )}

                <div className="bg-gray-50 p-4 rounded-lg mb-6">
                  <h2 className="text-lg font-medium mb-4">Parent/Guardian Information</h2>
                  <div className="bg-green-50 border border-green-200 rounded-lg p-3 mb-4">
                    <p className="text-green-800 text-sm">
                      <strong>Parent:</strong> {googleUserData?.firstname} {googleUserData?.lastname} ({googleUserData?.email})
                    </p>
                  </div>

                  <FormField
                    control={googleForm.control}
                    name="parentPhone"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-base">Phone Number</FormLabel>
                        <FormControl>
                          <Input
                            {...field}
                            type="tel"
                            placeholder="(*************"
                            className="h-14 text-base"
                            disabled={isSubmitting}
                            onChange={(e) => {
                              const formatted = formatPhoneNumber(e.target.value)
                              field.onChange(formatted)
                            }}
                            onKeyPress={(e) => {
                              const allowedKeys = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'Backspace', 'Delete', 'Tab', 'ArrowLeft', 'ArrowRight']
                              if (!allowedKeys.includes(e.key) && !e.ctrlKey && !e.metaKey) {
                                e.preventDefault()
                              }
                            }}
                            maxLength={14}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className="bg-gray-50 p-4 rounded-lg mb-6">
                  <h2 className="text-lg font-medium mb-4">Child Information</h2>
                  <div className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <FormField
                        control={googleForm.control}
                        name="childFirstName"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="text-base">Child First Name</FormLabel>
                            <FormControl>
                              <Input
                                {...field}
                                placeholder="Enter child first name"
                                className="h-14 text-base"
                                disabled={isSubmitting}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={googleForm.control}
                        name="childLastName"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="text-base">Child Last Name</FormLabel>
                            <FormControl>
                              <Input
                                {...field}
                                placeholder="Enter child last name"
                                className="h-14 text-base"
                                disabled={isSubmitting}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <FormField
                      control={googleForm.control}
                      name="childDob"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-base">Child Date of Birth</FormLabel>
                          <FormControl>
                            <Input {...field} type="date" className="h-14 text-base" disabled={isSubmitting} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={googleForm.control}
                      name="zipcode"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-base">Zip Code</FormLabel>
                          <FormControl>
                            <Input
                              {...field}
                              placeholder="Enter your zip code"
                              className="h-14 text-base"
                              disabled={isSubmitting}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </div>

                <div className="flex gap-4 pt-4">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => {
                      setShowGoogleForm(false);
                      setGoogleUserData(null);
                    }}
                    disabled={isSubmitting}
                    className="flex-1 h-14 text-base"
                  >
                    Back
                  </Button>
                  <Button
                    type="submit"
                    disabled={isSubmitting}
                    className="flex-1 h-14 text-base bg-orange-500 hover:bg-orange-600"
                  >
                    {isSubmitting ? "Creating Account..." : "Create Account with Google"}
                  </Button>
                </div>
              </form>
            </Form>
          </div>
        )}

        {!showGoogleForm && (
          <Form {...form}>
          <form id="registration-minor-form" onSubmit={form.handleSubmit(handleSubmit, onError)} className="space-y-6">
            {formError && (
              <div className="text-red-500 text-sm mb-4">{formError}</div>
            )}
            <div className="bg-gray-50 p-4 rounded-lg mb-6">
              <h2 className="text-lg font-medium mb-4">Parent/Guardian Information</h2>

              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="parentFirstName"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-base">First Name</FormLabel>
                        <FormControl>
                          <Input
                            {...field}
                            placeholder="Enter first name"
                            className={`h-14 text-base ${(apiError || form.formState.errors.parentFirstName) ? 'border-red-500' : ''}`}
                            disabled={isSubmitting}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="parentLastName"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-base">Last Name</FormLabel>
                        <FormControl>
                          <Input
                            {...field}
                            placeholder="Enter last name"
                            className={`h-14 text-base ${(apiError || form.formState.errors.parentLastName) ? 'border-red-500' : ''}`}
                            disabled={isSubmitting}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={form.control}
                  name="parentEmail"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-base">Email</FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          type="email"
                          placeholder="Enter email"
                          className={`h-14 text-base ${(apiError || form.formState.errors.parentEmail) ? 'border-red-500' : ''}`}
                          disabled={isSubmitting}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="parentPhone"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-base">Phone Number</FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          type="tel"
                          placeholder="(*************"
                          className={`h-14 text-base ${(apiError || form.formState.errors.parentPhone) ? 'border-red-500' : ''}`}
                          disabled={isSubmitting}
                          onChange={(e) => {
                            const formatted = formatPhoneNumber(e.target.value)
                            field.onChange(formatted)
                          }}
                          onKeyPress={(e) => {
                            const allowedKeys = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'Backspace', 'Delete', 'Tab', 'ArrowLeft', 'ArrowRight']
                            if (!allowedKeys.includes(e.key) && !e.ctrlKey && !e.metaKey) {
                              e.preventDefault()
                            }
                          }}
                          maxLength={14}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>

            <div className="bg-gray-50 p-4 rounded-lg mb-6">
              <h2 className="text-lg font-medium mb-4">Child Information</h2>

              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="childFirstName"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-base">First Name</FormLabel>
                        <FormControl>
                          <Input
                            {...field}
                            placeholder="Enter first name"
                            className={`h-14 text-base ${(apiError || form.formState.errors.childFirstName) ? 'border-red-500' : ''}`}
                            disabled={isSubmitting}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="childLastName"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-base">Last Name</FormLabel>
                        <FormControl>
                          <Input
                            {...field}
                            placeholder="Enter last name"
                            className={`h-14 text-base ${(apiError || form.formState.errors.childLastName) ? 'border-red-500' : ''}`}
                            disabled={isSubmitting}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={form.control}
                  name="childDob"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-base">Date of Birth</FormLabel>
                      <FormControl>
                        <Input {...field} type="date" className={`h-14 text-base ${(apiError || form.formState.errors.childDob) ? 'border-red-500' : ''}`} disabled={isSubmitting} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="childGender"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-base">Gender</FormLabel>
                      <FormControl>
                        <Select onValueChange={field.onChange} defaultValue={field.value} disabled={isSubmitting}>
                          <SelectTrigger className={`h-14 text-base ${(apiError || form.formState.errors.childGender) ? 'border-red-500' : ''}`}>
                            <SelectValue placeholder="Select gender" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="male">Male</SelectItem>
                            <SelectItem value="female">Female</SelectItem>
                            <SelectItem value="non-binary">Non-binary</SelectItem>
                            <SelectItem value="other">Other</SelectItem>
                            <SelectItem value="prefer-not-to-say">Prefer not to say</SelectItem>
                          </SelectContent>
                        </Select>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>

            <div className="space-y-4">
              <div className="bg-amber-50 border border-amber-100 rounded-lg p-3 flex items-start gap-2 mb-2">
                <AlertCircle className="text-amber-500 mt-0.5 flex-shrink-0" size={18} />
                <p className="text-amber-800 text-sm">
                  You can optionally provide the <strong>child's insurance information</strong> below to verify benefits.
                </p>
              </div>

              <FormField
                control={form.control}
                name="insurance"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-base">Child's Insurance (Optional)</FormLabel>
                    <FormControl>
                      <Select onValueChange={field.onChange} value={field.value} disabled={isSubmitting || insuranceLoading}>
                        <SelectTrigger className={`h-14 text-base ${(apiError || form.formState.errors.insurance) ? 'border-red-500' : ''}`}>
                          <SelectValue placeholder={insuranceLoading ? "Loading..." : "Select insurance"} />
                        </SelectTrigger>
                        <SelectContent>
                          {insuranceLoading && <SelectItem key="loading" value="loading">Loading...</SelectItem>}
                          {insuranceError && <SelectItem key="error" value="error">{insuranceError}</SelectItem>}
                          {!insuranceLoading && !insuranceError && insuranceOptions.length === 0 && (
                            <SelectItem key="no-data" value="no-data">No insurance companies found</SelectItem>
                          )}
                          {!insuranceLoading && insuranceOptions.map((opt, index) => (
                            <SelectItem key={opt._id || `insurance-${index}`} value={opt.organizationName}>{opt.organizationName}</SelectItem>
                          ))}
                          <SelectItem key="other" value="other">Other</SelectItem>
                        </SelectContent>
                      </Select>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="memberId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-base">Child's Member ID (Optional)</FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        placeholder="Enter member ID"
                        className={`h-14 text-base ${(apiError || form.formState.errors.memberId) ? 'border-red-500' : ''}`}
                        disabled={isSubmitting}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="password"
                render={({ field }) => (
                  <FormItem className="mb-8">
                    <FormLabel className="text-base">Password</FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        type="password"
                        placeholder="Create a password"
                        className={`h-14 text-base ${(apiError || form.formState.errors.password) ? 'border-red-500' : ''}`}
                        disabled={isSubmitting}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </form>
        </Form>
        )}
      </div>

      {!showGoogleForm && (
        <StickyBottomCTA
          primaryText="Create Account"
          primaryAction={() => {
            document
              .getElementById("registration-minor-form")
              ?.dispatchEvent(new Event("submit", { cancelable: true, bubbles: true }))
          }}
          secondaryText="Back"
          secondaryAction={() => router.back()}
          isDisabled={isSubmitting}
        />
      )}
    </main>
  )
}
