#!/bin/bash

echo "🧪 Testing Calendar API Endpoints..."
echo ""

# Test 1: Check if backend is running
echo "1. Testing backend connection..."
backend_response=$(curl -s -w "%{http_code}" http://localhost:3003/api/users/therapists -o /tmp/backend_test.json)
if [ "$backend_response" = "200" ]; then
    therapist_count=$(cat /tmp/backend_test.json | grep -o '"_id"' | wc -l)
    echo "✅ Backend connected - Found $therapist_count therapists"
else
    echo "❌ Backend connection failed - HTTP $backend_response"
    exit 1
fi
echo ""

# Test 2: Check if frontend is running
echo "2. Testing frontend connection..."
frontend_response=$(curl -s -w "%{http_code}" http://localhost:3000 -o /dev/null)
if [ "$frontend_response" = "200" ]; then
    echo "✅ Frontend connected - Status: $frontend_response"
else
    echo "❌ Frontend connection failed - HTTP $frontend_response"
    exit 1
fi
echo ""

# Test 3: Test therapist availability API
echo "3. Testing therapist availability API..."
therapist_id="675c597705d202e19eab9d06"  # Beth <PERSON>
month="2025-05"

availability_response=$(curl -s -w "%{http_code}" "http://localhost:3000/api/therapists/$therapist_id/available-days?month=$month" -o /tmp/availability_test.json)
if [ "$availability_response" = "200" ]; then
    working_hours=$(cat /tmp/availability_test.json | grep -o '"day"' | wc -l)
    blocked_dates=$(cat /tmp/availability_test.json | grep -o '"title":"Not available"' | wc -l)
    appointments=$(cat /tmp/availability_test.json | grep -o '"status"' | wc -l)
    
    echo "✅ Availability API working - Status: $availability_response"
    echo "   Working hours: $working_hours periods"
    echo "   Blocked dates: $blocked_dates blocks"
    echo "   Appointments: $appointments appointments"
else
    echo "❌ Availability API failed - HTTP $availability_response"
    cat /tmp/availability_test.json
fi
echo ""

# Test 4: Test insurance filtering
echo "4. Testing insurance filtering..."
insurance_response=$(curl -s -w "%{http_code}" "http://localhost:3003/api/users/therapists?state=North+Carolina" -o /tmp/insurance_test.json)
if [ "$insurance_response" = "200" ]; then
    nc_therapists=$(cat /tmp/insurance_test.json | grep -o '"_id"' | wc -l)
    echo "✅ Insurance filtering working - Found $nc_therapists therapists in NC"
    
    # Check if therapists have insurance data
    insurance_count=$(cat /tmp/insurance_test.json | grep -o '"insuranceCompanies"' | wc -l)
    echo "   Therapists with insurance field: $insurance_count"
else
    echo "❌ Insurance filtering failed - HTTP $insurance_response"
fi
echo ""

# Test 5: Test calendar page accessibility
echo "5. Testing calendar page..."
calendar_response=$(curl -s -w "%{http_code}" http://localhost:3000/calendar -o /dev/null)
if [ "$calendar_response" = "200" ]; then
    echo "✅ Calendar page accessible - Status: $calendar_response"
else
    echo "❌ Calendar page failed - HTTP $calendar_response"
fi
echo ""

# Test 6: Test therapists page
echo "6. Testing therapists page..."
therapists_page_response=$(curl -s -w "%{http_code}" http://localhost:3000/therapists -o /dev/null)
if [ "$therapists_page_response" = "200" ]; then
    echo "✅ Therapists page accessible - Status: $therapists_page_response"
else
    echo "❌ Therapists page failed - HTTP $therapists_page_response"
fi
echo ""

echo "🎉 Testing complete!"

# Cleanup
rm -f /tmp/backend_test.json /tmp/availability_test.json /tmp/insurance_test.json 