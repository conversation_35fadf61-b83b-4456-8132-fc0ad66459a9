"use client"

import { But<PERSON> } from "@/components/ui/button"

interface StickyBottomCTAProps {
  primaryText: string
  primaryAction: () => void
  secondaryText?: string
  secondaryAction?: () => void
  isDisabled?: boolean
}

export function StickyBottomCTA({
  primaryText,
  primaryAction,
  secondaryText,
  secondaryAction,
  isDisabled = false,
}: StickyBottomCTAProps) {
  return (
    <div className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 p-4 z-50">
      <div className="flex flex-col gap-2 max-w-md mx-auto">
        <Button
          onClick={primaryAction}
          disabled={isDisabled}
          className="h-14 text-base font-medium bg-[#F7903D] hover:bg-[#e67f2d] text-white"
        >
          {primaryText}
        </Button>

        {secondaryText && secondaryAction && (
          <Button
            onClick={secondaryAction}
            variant="outline"
            className="h-10 text-sm font-medium border-[#64748B] text-[#64748B]"
          >
            {secondaryText}
          </Button>
        )}
      </div>
    </div>
  )
}
