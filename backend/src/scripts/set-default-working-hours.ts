import mongoose from 'mongoose';
import { User } from '../models/User';
import dotenv from 'dotenv';

dotenv.config();

const defaultWorkingHours = [
  { day: 'monday', startTime: '09:00', endTime: '17:00' },
  { day: 'tuesday', startTime: '09:00', endTime: '17:00' },
  { day: 'wednesday', startTime: '09:00', endTime: '17:00' },
  { day: 'thursday', startTime: '09:00', endTime: '17:00' },
  { day: 'friday', startTime: '09:00', endTime: '17:00' }
];

async function setDefaultWorkingHours() {
  try {
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/lavni');
    console.log('Connected to MongoDB');

    const therapists = await User.find({ role: 'THERAPIST' });
    console.log(`Found ${therapists.length} therapists`);

    for (const therapist of therapists) {
      if (!therapist.workingHours || therapist.workingHours.length === 0) {
        await User.findOneAndUpdate(
          { _id: therapist._id },
          { $set: { workingHours: defaultWorkingHours } },
          { new: true }
        );
        console.log(`Updated working hours for ${therapist.firstname} ${therapist.lastname}`);
      }
    }

    console.log('Finished updating working hours');
    process.exit(0);
  } catch (error) {
    console.error('Error:', error);
    process.exit(1);
  }
}

setDefaultWorkingHours(); 