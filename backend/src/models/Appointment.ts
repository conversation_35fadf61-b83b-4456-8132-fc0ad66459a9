import mongoose, { Document, Schema } from 'mongoose';

export interface IAppointment extends Document {
  reminders: number[];
  status: 'PENDING' | 'CONFIRMED' | 'CANCELLED';
  approvedStatus: 'APPROVED' | 'PENDING' | 'REJECTED';
  therapistId: Schema.Types.ObjectId;
  clientId: Schema.Types.ObjectId;
  start: Date;
  end: Date;
  title: string;
  typeOfMeeting: 'VIDEO' | 'AUDIO' | 'IN_PERSON';
  createdBy: Schema.Types.ObjectId;
  color: string;
  groupId?: string;
  repeatInfo?: {
    repeatType: string;
    interval: string;
    repeatDays: {
      sunday: boolean;
      monday: boolean;
      tuesday: boolean;
      wednesday: boolean;
      thursday: boolean;
      friday: boolean;
      saturday: boolean;
    };
    endingDate: Date;
    endingAfter: number;
    endingType: string;
  };
}

const appointmentSchema = new Schema<IAppointment>(
  {
    reminders: {
      type: [Number],
      default: [30], // Default 30-minute reminder
    },
    status: {
      type: String,
      enum: ['PENDING', 'CONFIRMED', 'CANCELLED'],
      default: 'PENDING',
    },
    approvedStatus: {
      type: String,
      enum: ['APPROVED', 'PENDING', 'REJECTED'],
      default: 'PENDING',
    },
    therapistId: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true,
    },
    clientId: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true,
    },
    start: {
      type: Date,
      required: true,
    },
    end: {
      type: Date,
      required: true,
    },
    title: {
      type: String,
      default: 'Lavni Therapy session',
    },
    typeOfMeeting: {
      type: String,
      enum: ['VIDEO', 'AUDIO', 'IN_PERSON'],
      required: true,
    },
    createdBy: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true,
    },
    color: {
      type: String,
      default: '#FF6900',
    },
    groupId: String,
    repeatInfo: {
      repeatType: {
        type: String,
        default: 'DOES NOT REPEAT',
      },
      interval: String,
      repeatDays: {
        sunday: { type: Boolean, default: false },
        monday: { type: Boolean, default: false },
        tuesday: { type: Boolean, default: false },
        wednesday: { type: Boolean, default: false },
        thursday: { type: Boolean, default: false },
        friday: { type: Boolean, default: false },
        saturday: { type: Boolean, default: false },
      },
      endingDate: Date,
      endingAfter: Number,
      endingType: String,
    },
  },
  {
    timestamps: true,
  }
);

// Middleware to validate appointment times
appointmentSchema.pre('save', function(next) {
  if (this.end <= this.start) {
    next(new Error('End time must be after start time'));
  }
  next();
});

// Index for efficient queries
appointmentSchema.index({ therapistId: 1, start: 1 });
appointmentSchema.index({ clientId: 1, start: 1 });
appointmentSchema.index({ status: 1 });

export const Appointment = mongoose.model<IAppointment>('Appointment', appointmentSchema); 