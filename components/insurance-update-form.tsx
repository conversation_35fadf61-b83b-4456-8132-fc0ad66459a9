"use client"

import { useState, useEffect } from "react"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"
import { Button } from "@/components/ui/button"
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import axios from "axios"

const formSchema = z.object({
  state: z.string().min(1, { message: "State is required" }),
  carrier: z.string().min(1, { message: "Insurance carrier is required" }),
  memberId: z.string().min(1, { message: "Member ID is required" }),
  firstName: z.string().min(1, { message: "First name is required" }),
  lastName: z.string().min(1, { message: "Last name is required" }),
  dob: z.string().min(1, { message: "Date of birth is required" }),
})

type FormValues = z.infer<typeof formSchema>

interface InsuranceUpdateFormProps {
  onSubmit: (values: FormValues) => void
  userData?: any
}

export function InsuranceUpdateForm({ onSubmit, userData }: InsuranceUpdateFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [insuranceOptions, setInsuranceOptions] = useState<any[]>([])
  const [loadingInsurance, setLoadingInsurance] = useState(true)
  const [insuranceError, setInsuranceError] = useState<string | null>(null)
  const [selectedState, setSelectedState] = useState<string>("")

  // Get state from localStorage or userData
  useEffect(() => {
    const storedState = typeof window !== 'undefined' ? localStorage.getItem('selectedState') : null;
    setSelectedState(storedState || userData?.state || "");
  }, [userData])

  // Fetch insurance companies
  useEffect(() => {
    const fetchInsurance = async () => {
      setLoadingInsurance(true)
      setInsuranceError(null)
      try {
        const res = await axios.get("/api/insurance-companies")
        setInsuranceOptions(res.data.data || [])
      } catch (err) {
        setInsuranceError("Could not load insurance companies")
      } finally {
        setLoadingInsurance(false)
      }
    }
    fetchInsurance()
  }, [])

  // Extract unique states
  const uniqueStates = Array.from(new Set(insuranceOptions.flatMap(opt => opt.states || []))).sort();

  // Filter insurance companies by selected state
  const filteredInsuranceOptions = selectedState
    ? insuranceOptions.filter(opt => (opt.states || []).includes(selectedState) || (opt.states || []).includes("All"))
    : [];

  // Sort the filtered options alphabetically
  const sortedInsuranceOptions = filteredInsuranceOptions.sort((a, b) => a.organizationName.localeCompare(b.organizationName));

  // Extract first and last name from userData if available
  const getDefaultFirstName = () => {
    if (userData?.firstName) return userData.firstName
    if (userData?.childFirstName) return userData.childFirstName
    return ""
  }

  const getDefaultLastName = () => {
    if (userData?.lastName) return userData.lastName
    if (userData?.childLastName) return userData.childLastName
    return ""
  }

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      state: selectedState,
      carrier: "",
      memberId: "",
      firstName: getDefaultFirstName(),
      lastName: getDefaultLastName(),
      dob: userData?.dob || userData?.childDob || "",
    },
  })

  // Update selectedState when form changes
  useEffect(() => {
    const subscription = form.watch((value) => {
      if (value.state && value.state !== selectedState) {
        setSelectedState(value.state)
        if (typeof window !== 'undefined') {
          localStorage.setItem('selectedState', value.state)
        }
      }
    });
    return () => subscription.unsubscribe();
  }, [form, selectedState])

  const handleSubmit = async (values: FormValues) => {
    setIsSubmitting(true)
    try {
      onSubmit(values)
    } catch (error) {
      console.error("Form submission error:", error)
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
        {/* State Selector */}
        <FormField
          control={form.control}
          name="state"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="text-base">State</FormLabel>
              <FormControl>
                <Select
                  onValueChange={field.onChange}
                  value={field.value}
                  disabled={isSubmitting || loadingInsurance}
                >
                  <SelectTrigger className="h-14 text-base">
                    <SelectValue placeholder={loadingInsurance ? "Loading..." : "Select your state"} />
                  </SelectTrigger>
                  <SelectContent>
                    {loadingInsurance && <SelectItem value="loading">Loading...</SelectItem>}
                    {insuranceError && <SelectItem value="error">{insuranceError}</SelectItem>}
                    {!loadingInsurance && uniqueStates.length === 0 && (
                      <SelectItem value="no-data">No states found</SelectItem>
                    )}
                    {!loadingInsurance && uniqueStates.map((state, index) => (
                      <SelectItem key={state || `state-${index}`} value={state}>{state}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        {/* Insurance Carrier Dropdown */}
        <FormField
          control={form.control}
          name="carrier"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="text-base">Insurance Carrier</FormLabel>
              <FormControl>
                <Select onValueChange={field.onChange} value={field.value} disabled={isSubmitting || loadingInsurance || !form.watch("state")}> 
                  <SelectTrigger className="h-14 text-base">
                    <SelectValue placeholder={loadingInsurance ? "Loading..." : (!form.watch("state") ? "Select a state first" : "Select your insurance")} />
                  </SelectTrigger>
                  <SelectContent>
                    {loadingInsurance && <SelectItem value="loading">Loading...</SelectItem>}
                    {insuranceError && <SelectItem value="error">{insuranceError}</SelectItem>}
                    {!loadingInsurance && form.watch("state") && sortedInsuranceOptions.length === 0 && (
                      <SelectItem value="no-data">No insurance companies found</SelectItem>
                    )}
                    {!loadingInsurance && form.watch("state") && sortedInsuranceOptions.map((opt, index) => (
                      <SelectItem key={opt._id || `insurance-${index}`} value={opt.organizationName}>{opt.organizationName}</SelectItem>
                    ))}
                    <SelectItem value="other">Other</SelectItem>
                  </SelectContent>
                </Select>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="memberId"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="text-base">Member ID</FormLabel>
              <FormControl>
                <Input
                  {...field}
                  placeholder="Enter your member ID"
                  className="h-14 text-base"
                  disabled={isSubmitting}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="grid grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="firstName"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-base">First Name</FormLabel>
                <FormControl>
                  <Input {...field} placeholder="Enter first name" className="h-14 text-base" disabled={isSubmitting} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="lastName"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-base">Last Name</FormLabel>
                <FormControl>
                  <Input {...field} placeholder="Enter last name" className="h-14 text-base" disabled={isSubmitting} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <FormField
          control={form.control}
          name="dob"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="text-base">Date of Birth</FormLabel>
              <FormControl>
                <Input {...field} type="date" className="h-14 text-base" disabled={isSubmitting} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <Button
          type="submit"
          className="w-full h-14 mt-4 bg-[#F7903D] hover:bg-[#e67f2d] text-white"
          disabled={isSubmitting}
        >
          Update Insurance
        </Button>
      </form>
    </Form>
  )
}
