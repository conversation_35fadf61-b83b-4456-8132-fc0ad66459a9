import mongoose, { Document, Schema, Types, model } from 'mongoose';

export interface IInsuranceCompany extends Document {
  organizationName: string;
  states: string[];
  isMedicaid: boolean;
  type: string;
  tradingPartnerServiceId: string;
}

const insuranceCompanySchema = new Schema<IInsuranceCompany>({
  organizationName: { type: String, required: true },
  states: { type: [String], default: [] },
  isMedicaid: { type: Boolean, default: false },
  type: { type: String, default: '' },
  tradingPartnerServiceId: { type: String, required: true },
}, { timestamps: true });

export const InsuranceCompany = mongoose.model<IInsuranceCompany>('InsuranceCompany', insuranceCompanySchema);

export interface IInsurance extends Document {
  clientId: Types.ObjectId;
  insuranceCompanyId: Types.ObjectId;
  subscriber: {
    memberId?: string;
    policyNumber?: string;
    firstName?: string;
    lastName?: string;
    gender?: string;
    dateOfBirth?: string;
    paymentResponsibilityLevelCode?: string;
    address?: {
      address1?: string;
      city?: string;
      state?: string;
      postalCode?: string;
    };
  };
  dependent?: {
    memberId?: string;
    policyNumber?: string;
    firstName?: string;
    lastName?: string;
    gender?: string;
    dateOfBirth?: string;
    paymentResponsibilityLevelCode?: string;
    relationshipToSubscriberCode?: string;
    address?: {
      address1?: string;
      city?: string;
      state?: string;
      postalCode?: string;
    };
  };
  insuranceCardId?: Types.ObjectId;
  controlNumber?: string;
  insuranceAccessToken?: string;
}

const insuranceSchema = new Schema<IInsurance>({
  clientId: { type: Schema.Types.ObjectId, ref: 'User', required: true },
  insuranceCompanyId: { type: Schema.Types.ObjectId, ref: 'InsuranceCompany', required: false },
  subscriber: {
    memberId: String,
    policyNumber: String,
    firstName: String,
    lastName: String,
    gender: String,
    dateOfBirth: String,
    paymentResponsibilityLevelCode: String,
    address: {
      address1: String,
      city: String,
      state: String,
      postalCode: String,
    },
  },
  dependent: {
    memberId: String,
    policyNumber: String,
    firstName: String,
    lastName: String,
    gender: String,
    dateOfBirth: String,
    paymentResponsibilityLevelCode: String,
    relationshipToSubscriberCode: String,
    address: {
      address1: String,
      city: String,
      state: String,
      postalCode: String,
    },
  },
  insuranceCardId: { type: Schema.Types.ObjectId, ref: 'Upload' },
  controlNumber: String,
  insuranceAccessToken: String,
}, { timestamps: true });

export const Insurance = model<IInsurance>('Insurance', insuranceSchema); 