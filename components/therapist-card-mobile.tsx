"use client"

import Image from "next/image"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { PlayCircle } from "lucide-react"
import { Therapist } from "@/lib/api"
import { useState, useEffect } from "react"
import { SpecialtyBadge } from "@/components/ui/specialty-badge"

interface TherapistCardMobileProps {
  therapist: Therapist
  onSelect: (therapist: Therapist) => void
  onProfileOpen: (therapist: Therapist) => void
}

export function TherapistCardMobile({ therapist, onSelect, onProfileOpen }: TherapistCardMobileProps) {
  const SPECIALTIES_PER_PAGE = 4;
  const [specialtyIndex, setSpecialtyIndex] = useState(0);

  useEffect(() => {
    if (!therapist.specialties || therapist.specialties.length <= SPECIALTIES_PER_PAGE) return;
    const interval = setInterval(() => {
      setSpecialtyIndex((prev) =>
        prev + SPECIALTIES_PER_PAGE >= therapist.specialties.length ? 0 : prev + SPECIALTIES_PER_PAGE
      );
    }, 5000); // 5 seconds per rotation
    return () => clearInterval(interval);
  }, [therapist.specialties]);

  const visibleSpecialties = therapist.specialties.slice(
    specialtyIndex,
    specialtyIndex + SPECIALTIES_PER_PAGE
  );

  return (
    <div className="border border-gray-200 rounded-lg overflow-hidden bg-white shadow-sm">
      <div className="p-4">
        <div className="flex flex-col items-center mb-4">
          <div
            className="relative w-32 h-32 rounded-full overflow-hidden cursor-pointer mb-3 border-2 border-[#FEC07E]"
            onClick={() => onProfileOpen(therapist)}
          >
            <Image 
              src={therapist.image || "/placeholder.svg"} 
              alt={therapist.name} 
              fill 
              className="object-cover" 
              onError={(e) => {
                // Fallback nếu ảnh lỗi
                const target = e.target as HTMLImageElement;
                target.src = "/placeholder.svg";
              }}
            />
          </div>

          <div className="text-center">
            <h3 className="text-lg font-semibold text-gray-900 cursor-pointer" onClick={() => onProfileOpen(therapist)}>
              {therapist.name}
            </h3>
            <p className="text-gray-600">{therapist.title}</p>
          </div>
        </div>

        <div className="flex flex-wrap gap-1.5 justify-center mb-3">
          {visibleSpecialties.map((specialty, index) => (
            <SpecialtyBadge 
              key={index} 
              text={specialty}
              size="sm"
              variant={index % 3 === 0 ? "primary" : index % 3 === 1 ? "secondary" : "outline"}
            />
          ))}
        </div>

        <p className="text-gray-700 text-sm text-center mb-4 line-clamp-3 cursor-pointer" onClick={() => onProfileOpen(therapist)}>{therapist.bio}</p>

        <div className="mt-4 flex items-center justify-center">
          {therapist.nextAvailability ? (
            <span className="text-sm font-medium text-[#0f3460]">
              Next availability: {formatNextAvailability(therapist.nextAvailability)}
            </span>
          ) : (
            <span className="text-sm text-gray-400">No availability</span>
          )}
        </div>

        <Button onClick={() => onSelect(therapist)} className="w-full mt-4 bg-[#F7903D] hover:bg-[#e67f2d] text-white">
          Schedule Appointment
        </Button>
      </div>
    </div>
  )
}

// Helper to format nextAvailability
function formatNextAvailability(availabilityString: string): string {
  // Backend now returns formatted strings like "Tuesday, June 4th at 11:30 AM"
  // Just return it as-is since it's already properly formatted
  return availabilityString || '';
}
