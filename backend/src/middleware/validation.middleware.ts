import { Request, Response, NextFunction } from 'express';
import <PERSON><PERSON> from 'joi';
import { AppError } from './error.middleware';

const registrationSchema = Joi.object({
  email: Joi.string().email().required(),
  password: Joi.string().min(8).required(),
  firstname: Joi.string().required(),
  lastname: Joi.string().required(),
  role: Joi.string().valid('CLIENT', 'THERAPIST').required(),
  dateOfBirth: Joi.date().required(),
  city: Joi.string(),
  state: Joi.string(),
  gender: Joi.string(),
  primaryPhone: Joi.string(),
});

const loginSchema = Joi.object({
  email: Joi.string().email().required(),
  password: Joi.string().required(),
});

const appointmentSchema = Joi.object({
  therapistId: Joi.string().required(),
  clientId: Joi.string().required(),
  start: Joi.date().required(),
  end: Joi.date().required(),
  title: Joi.string(),
  typeOfMeeting: Joi.string().valid('VIDEO', 'AUDIO', 'IN_PERSON').required(),
  reminders: Joi.array().items(Joi.number()),
  repeatInfo: Joi.object({
    repeatType: Joi.string().required(),
    interval: Joi.string(),
    repeatDays: Joi.object({
      sunday: Joi.boolean(),
      monday: Joi.boolean(),
      tuesday: Joi.boolean(),
      wednesday: Joi.boolean(),
      thursday: Joi.boolean(),
      friday: Joi.boolean(),
      saturday: Joi.boolean(),
    }),
    endingDate: Joi.date(),
    endingAfter: Joi.number(),
    endingType: Joi.string(),
  }),
});

export const validateRegistration = (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const { error } = registrationSchema.validate(req.body);
  if (error) {
    return next(new AppError(error.details[0].message, 400));
  }
  next();
};

export const validateLogin = (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const { error } = loginSchema.validate(req.body);
  if (error) {
    return next(new AppError(error.details[0].message, 400));
  }
  next();
};

export const validateAppointment = (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const { error } = appointmentSchema.validate(req.body);
  if (error) {
    return next(new AppError(error.details[0].message, 400));
  }
  next();
}; 