"use client"

import { useRouter } from "next/navigation"
import { useState, useEffect } from "react"
import { StickyBottomCTA } from "@/components/sticky-bottom-cta"
import { MobileStepTracker } from "@/components/mobile-step-tracker"
import { InsuranceUpdateForm } from "@/components/insurance-update-form"
import { CreditCardForm } from "@/components/credit-card-form"
import { CheckCircle, AlertCircle, CreditCard } from "lucide-react"
import { format } from "date-fns"
import { Button } from "@/components/ui/button"
import { DesktopLayout } from "@/components/desktop-layout"
import { MobileHeader } from "@/components/mobile-header"
import { toast } from "sonner"

export default function ConfirmationPage() {
  const router = useRouter()
  const [selectedTherapist, setSelectedTherapist] = useState<any>(null)
  const [appointmentDetails, setAppointmentDetails] = useState<any>(null)
  const [userData, setUserData] = useState<any>(null)
  const [insuranceStatus, setInsuranceStatus] = useState<"verified" | "not-found" | "pending" | "skipped">("pending")
  const [copayAmount, setCopayAmount] = useState<number | null>(null)
  const [showInsuranceForm, setShowInsuranceForm] = useState(false)
  const [showPaymentForm, setShowPaymentForm] = useState(false)
  const [paymentStatus, setPaymentStatus] = useState<"pending" | "completed" | "skipped">("pending")
  const [copayMessage, setCopayMessage] = useState<string | null>(null)
  const [hasExplicitlySkippedInsurance, setHasExplicitlySkippedInsurance] = useState(false)
  const [showSecondaryInsuranceForm, setShowSecondaryInsuranceForm] = useState(false)
  const [isVerifyingSecondaryInsurance, setIsVerifyingSecondaryInsurance] = useState(false)

  useEffect(() => {
    // Retrieve data from localStorage
    const storedTherapist = localStorage.getItem("selectedTherapist")
    const storedAppointment = localStorage.getItem("appointmentDetails")
    const storedUserData = localStorage.getItem("userData")
    if (storedTherapist) setSelectedTherapist(JSON.parse(storedTherapist))
    if (storedAppointment) setAppointmentDetails(JSON.parse(storedAppointment))
    if (storedUserData) setUserData(JSON.parse(storedUserData))

    // --- Eligibility check logic ---
    const eligibilityResultRaw = localStorage.getItem("eligibilityResult");
    const userData = storedUserData ? JSON.parse(storedUserData) : null;
    
    // Check if user provided insurance information during registration
    const hasInsuranceInfo = userData?.insurance && userData?.memberId;
    
    if (eligibilityResultRaw) {
      try {
        const eligibilityResult = JSON.parse(eligibilityResultRaw);
        if (eligibilityResult.eligibilityStatus === "ACTIVE") {
          setInsuranceStatus("verified");
          setCopayAmount(eligibilityResult.copayAmount || 0);
          setCopayMessage(eligibilityResult.copayMessage || null);
        } else {
          setInsuranceStatus("not-found");
          setShowInsuranceForm(false);
        }
      } catch (e) {
        setInsuranceStatus("not-found");
        setShowInsuranceForm(false);
      }
    } else if (hasInsuranceInfo) {
      // User provided insurance info but no eligibility result - something went wrong
      setInsuranceStatus("not-found");
    } else {
      // User didn't provide insurance information - skip insurance verification
      setInsuranceStatus("skipped");
    }
  }, [])

  const handleFinish = async () => {
    try {
      // Get user registration data
      const registeredUser = localStorage.getItem("registeredUser");
      const userData = localStorage.getItem("userData");
      const password = localStorage.getItem("password");
      
      if (!registeredUser || !userData) {
        console.error("Missing user data for auto-login");
        toast.error("Missing user data. Please try logging in manually.");
        window.location.href = "https://mylavni.com/signin/";
        return;
      }

      const user = JSON.parse(registeredUser);
      const userFormData = JSON.parse(userData);
      
      // Check if user registered with Google or email
      const isGoogleRegistration = userFormData.registrationType === 'google';
      console.log("isGoogleRegistration", isGoogleRegistration);
      
      let loginResponse;
      
      if (isGoogleRegistration) {
        // Auto-login with Google - redirect to external signin page
        toast.info("Please log in with Google to access your dashboard");
        window.location.href = "https://mylavni.com/signin/";
        return;
      } else {
        // Auto-login with email and password
        if (!password) {
          console.error("Password not found for email login");
          toast.error("Password not found. Please log in manually.");
          window.location.href = "https://mylavni.com/signin/";
          return;
        }

        const passwordValue = JSON.parse(password);
        
        console.log("Attempting auto-login with email...");
        
        loginResponse = await fetch("https://api.lavnihealth.com/api/public/login", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            email: userFormData.email || userFormData.parentEmail,
            password: passwordValue,
            medium: "EMAIL"
          }),
        });

        const loginData = await loginResponse.json();
        
        if (loginResponse.ok && loginData.success) {
          // Store auth token with the key "token" in localStorage (for current domain)
          localStorage.setItem("token", loginData.data.authToken || loginData.data);
          localStorage.setItem("currentUser", JSON.stringify(loginData.data));
          
          // Set cookie for parent domain so mylavni.com can access it
          const token = loginData.data.authToken || loginData.data;
          
          
          console.log("Auto-login successful, redirecting to dashboard");
          toast.success("Welcome to your Lavni dashboard!");
          
          // Redirect to external dashboard
          window.location.href = "https://mylavni.com/dashboard?token=" + token;
        } else {
          console.error("Auto-login failed:", loginData);
          toast.error("Auto-login failed. Please log in manually.");
          window.location.href = "https://mylavni.com/signin/";
        }
      }
      
    } catch (error) {
      console.error("Auto-login error:", error);
      toast.error("Login failed. Please try logging in manually.");
      window.location.href = "https://mylavni.com/signin/";
    }
  }

  const handleInsuranceUpdate = async (data: any) => {
    // Save updated insurance info to localStorage
    const updatedUserData = { ...userData, ...data };
    localStorage.setItem("userData", JSON.stringify(updatedUserData));
    setUserData(updatedUserData);
    setShowInsuranceForm(false);
    setInsuranceStatus("pending");
    
    // Get user ID from registered user data
    const registeredUser = localStorage.getItem("registeredUser");
    let userId = null;
    if (registeredUser) {
      try {
        const user = JSON.parse(registeredUser);
        userId = user._id;
      } catch (e) {
        console.error("Error parsing registered user data:", e);
      }
    }

    // Build eligibility payload
    const today = new Date();
    const serviceDate = `${today.getFullYear()}${String(today.getMonth()+1).padStart(2,'0')}${String(today.getDate()).padStart(2,'0')}`;
    let insuranceCompanyId = "";
    try {
      const res = await fetch("/api/insurance-companies");
      const companies = await res.json();
      if (companies && companies.data && Array.isArray(companies.data)) {
        const found = companies.data.find((c: any) => c.organizationName === data.carrier);
        insuranceCompanyId = found?.tradingPartnerServiceId || "";
      }
    } catch (e) {}
    
    const eligibilityPayload = {
      firstName: data.firstName,
      lastName: data.lastName,
      insuranceCompanyId,
      dateOfBirth: data.dob.replace(/-/g, ""),
      relationshipCode: "18",
      serviceDate,
      gender: userData?.gender === "male" ? "M" : userData?.gender === "female" ? "F" : "U",
      memberId: data.memberId,
    };
    
    if (eligibilityPayload.firstName && eligibilityPayload.lastName && eligibilityPayload.insuranceCompanyId && eligibilityPayload.dateOfBirth && eligibilityPayload.gender && eligibilityPayload.memberId) {
      const eligRes = await fetch("/api/check-eligibility", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(eligibilityPayload),
      });
      const eligData = await eligRes.json();
      localStorage.setItem("eligibilityResult", JSON.stringify(eligData));
      
      // Save insurance to database if user ID is available
      if (userId) {
        try {
          const insuranceData = {
            carrier: data.carrier,
            memberId: data.memberId,
            firstName: data.firstName,
            lastName: data.lastName,
            dob: data.dob,
            state: data.state
          };

          await fetch("/api/save-insurance", {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({
              userId,
              insuranceData,
              eligibilityResult: eligData,
              isSecondary: false
            }),
          });
          
          console.log("Primary insurance saved to database successfully");
        } catch (error) {
          console.error("Error saving primary insurance to database:", error);
        }
      }
      
      if (eligData.eligibilityStatus === "ACTIVE") {
        setInsuranceStatus("verified");
        setCopayAmount(eligData.copayAmount || 0);
        setCopayMessage(eligData.copayMessage || null);
      } else {
        setInsuranceStatus("not-found");
        setShowInsuranceForm(false);
      }
    } else {
      setInsuranceStatus("not-found");
      setShowInsuranceForm(false);
    }
  }

  const handleSkipInsurance = () => {
    setInsuranceStatus("skipped")
    setShowInsuranceForm(false)
    setHasExplicitlySkippedInsurance(true)
    setCopayAmount(0) // Assume no copay if insurance is skipped
    setPaymentStatus("completed")
  }

  const handlePaymentSubmit = (data: any) => {
    // In a real app, this would process the payment
    console.log("Payment data:", data)

    // Simulate successful payment
    setPaymentStatus("completed")
    setShowPaymentForm(false)
  }

  const handleSkipPayment = () => {
    setPaymentStatus("skipped")
    setShowPaymentForm(false)
  }

  const handleSecondaryInsuranceUpdate = async (data: any) => {
    setIsVerifyingSecondaryInsurance(true);
    setShowSecondaryInsuranceForm(false);
    
    // Get user ID from registered user data
    const registeredUser = localStorage.getItem("registeredUser");
    let userId = null;
    if (registeredUser) {
      try {
        const user = JSON.parse(registeredUser);
        userId = user._id;
      } catch (e) {
        console.error("Error parsing registered user data:", e);
      }
    }
    
    try {
      // Build eligibility payload for secondary insurance
      const today = new Date();
      const serviceDate = `${today.getFullYear()}${String(today.getMonth()+1).padStart(2,'0')}${String(today.getDate()).padStart(2,'0')}`;
      let insuranceCompanyId = "";
      
      const res = await fetch("/api/insurance-companies");
      const companies = await res.json();
      if (companies && companies.data && Array.isArray(companies.data)) {
        const found = companies.data.find((c: any) => c.organizationName === data.carrier);
        insuranceCompanyId = found?.tradingPartnerServiceId || "";
      }
      
      const eligibilityPayload = {
        firstName: data.firstName,
        lastName: data.lastName,
        insuranceCompanyId,
        dateOfBirth: data.dob.replace(/-/g, ""),
        relationshipCode: "18",
        serviceDate,
        gender: userData?.gender === "male" ? "M" : userData?.gender === "female" ? "F" : "U",
        memberId: data.memberId,
      };
      
      if (eligibilityPayload.firstName && eligibilityPayload.lastName && eligibilityPayload.insuranceCompanyId && eligibilityPayload.dateOfBirth && eligibilityPayload.gender && eligibilityPayload.memberId) {
        const eligRes = await fetch("/api/check-eligibility", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(eligibilityPayload),
        });
        const eligData = await eligRes.json();
        
        // Save secondary insurance to database if user ID is available
        if (userId) {
          try {
            const insuranceData = {
              carrier: data.carrier,
              memberId: data.memberId,
              firstName: data.firstName,
              lastName: data.lastName,
              dob: data.dob,
              state: data.state
            };

            await fetch("/api/save-insurance", {
              method: "POST",
              headers: { "Content-Type": "application/json" },
              body: JSON.stringify({
                userId,
                insuranceData,
                eligibilityResult: eligData,
                isSecondary: true
              }),
            });
            
            console.log("Secondary insurance saved to database successfully");
          } catch (error) {
            console.error("Error saving secondary insurance to database:", error);
          }
        }
        
        if (eligData.eligibilityStatus === "ACTIVE") {
          // Update with secondary insurance results
          localStorage.setItem("secondaryInsuranceResult", JSON.stringify(eligData));
          setCopayAmount(eligData.copayAmount || 0);
          setCopayMessage(eligData.copayMessage || null);
          
          // If secondary insurance reduces copay to 0, mark payment as completed
          if (eligData.copayAmount === 0) {
            setPaymentStatus("completed");
          }
        }
      }
    } catch (error) {
      console.error("Secondary insurance verification failed:", error);
    } finally {
      setIsVerifyingSecondaryInsurance(false);
    }
  }

  const formatAppointmentDate = () => {
    if (!appointmentDetails?.date || !appointmentDetails?.timeSlot) {
      return "your scheduled time";
    }
    // Try to parse the timeSlot as a time string, otherwise just show it
    const date = new Date(appointmentDetails.date);
    let timeString = appointmentDetails.timeSlot;
    // If timeSlot is an ISO string, format it
    if (!isNaN(Date.parse(timeString))) {
      const time = new Date(timeString);
      timeString = time.toLocaleTimeString([], { hour: 'numeric', minute: '2-digit', hour12: true });
    }
    return `${format(date, "EEEE, MMMM d, yyyy")} at ${timeString}`;
  }

  // Get patient's full name
  const getPatientName = () => {
    if (userData?.firstName && userData?.lastName) {
      return `${userData.firstName} ${userData.lastName}`
    } else if (userData?.childFirstName && userData?.childLastName) {
      return `${userData.childFirstName} ${userData.childLastName}`
    } else {
      return "the patient"
    }
  }

  const isSetupComplete = () => {
    // Setup is complete if:
    // 1. Insurance is verified (with actual eligibility check) or explicitly skipped, AND
    // 2. Payment is completed or skipped (or not needed due to $0 copay)
    // 3. Not currently verifying secondary insurance
    
    const insuranceComplete = (
      (insuranceStatus === "verified") || 
      (insuranceStatus === "skipped" && hasExplicitlySkippedInsurance)
    );
    
    const paymentComplete = (
      paymentStatus === "completed" ||
      paymentStatus === "skipped" ||
      copayAmount === 0 ||
      copayAmount === null
    );
    
    return insuranceComplete && paymentComplete && !isVerifyingSecondaryInsurance;
  }

  // Desktop confirmation content
  const ConfirmationContent = () => (
    <div className="space-y-6">
      <div className="bg-green-50 border border-green-100 rounded-lg p-4 flex items-start gap-3">
        <CheckCircle className="text-green-500 mt-0.5 flex-shrink-0" size={20} />
        <div>
          <p className="text-green-800 font-medium">
            Appointment for {getPatientName()} with {selectedTherapist?.name || "your therapist"} on{" "}
            {formatAppointmentDate()} is confirmed.
          </p>
          <p className="text-green-700 text-sm mt-1">You'll receive a confirmation email with details.</p>
        </div>
      </div>

      {/* Insurance Section */}
      {insuranceStatus === "pending" && (
        <div className="bg-gray-50 border border-gray-200 rounded-lg p-4 flex items-center justify-center h-24">
          <div className="animate-pulse flex flex-col items-center">
            <p className="text-gray-600">Verifying insurance...</p>
          </div>
        </div>
      )}

      {insuranceStatus === "verified" && (
        <div className="bg-green-50 border border-green-100 rounded-lg p-4 flex items-start gap-3">
          <CheckCircle className="text-green-500 mt-0.5 flex-shrink-0" size={20} />
          <div>
            <p className="text-green-800 font-medium">Insurance verified</p>
            {copayAmount === 0 ? (
              <p className="text-green-700 text-sm mt-1">Great news! No copayment required for your session.</p>
            ) : copayAmount && copayAmount > 0 ? (
              <p className="text-green-700 text-sm mt-1">You have a copayment. A member of our team will reach out to confirm the amount.</p>
            ) : (
              <p className="text-green-700 text-sm mt-1">Insurance benefits verified successfully.</p>
            )}
          </div>
        </div>
      )}

      {insuranceStatus === "skipped" && !hasExplicitlySkippedInsurance && (
        <div className="bg-blue-50 border border-blue-100 rounded-lg p-4 flex items-start gap-3">
          <AlertCircle className="text-blue-500 mt-0.5 flex-shrink-0" size={20} />
          <div className="flex-1">
            <p className="text-blue-800 font-medium">Add your insurance information</p>
            <p className="text-blue-700 text-sm mt-1">
              Adding your insurance can help reduce your session costs. You can add it now or skip for later.
            </p>
            <div className="mt-3 flex flex-col gap-2 sm:flex-row">
              <Button
                size="sm"
                onClick={() => setShowInsuranceForm(true)}
                className="bg-[#F7903D] hover:bg-[#e67f2d] text-white"
              >
                Add Insurance Information
              </Button>
              <Button 
                size="sm" 
                variant="outline" 
                onClick={() => {
                  // Keep it skipped but allow user to proceed
                  setHasExplicitlySkippedInsurance(true);
                }}
              >
                Skip for now
              </Button>
            </div>
          </div>
        </div>
      )}

      {insuranceStatus === "skipped" && hasExplicitlySkippedInsurance && (
        <div className="bg-amber-50 border border-amber-100 rounded-lg p-4 flex items-start gap-3">
          <AlertCircle className="text-amber-500 mt-0.5 flex-shrink-0" size={20} />
          <div>
            <p className="text-amber-800 font-medium">Insurance information skipped</p>
            <p className="text-amber-700 text-sm mt-1">
              You can add your insurance information later in your account settings.
            </p>
          </div>
        </div>
      )}

      {insuranceStatus === "not-found" && !showInsuranceForm && (
        <div className="bg-amber-50 border border-amber-100 rounded-lg p-4 flex items-start gap-3">
          <AlertCircle className="text-amber-500 mt-0.5 flex-shrink-0" size={20} />
          <div>
            <p className="text-amber-800 font-medium">We could not verify your insurance information.</p>
            <p className="text-amber-700 text-sm mt-1">Please update your insurance details and try again.</p>
            <div className="mt-3 flex gap-2">
              <Button
                size="sm"
                onClick={() => setShowInsuranceForm(true)}
                className="bg-[#F7903D] hover:bg-[#e67f2d] text-white"
              >
                Update Insurance Information
              </Button>
            </div>
          </div>
        </div>
      )}

      {showInsuranceForm && (
        <div className="mt-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-medium">Update Insurance Information</h3>
            <Button variant="ghost" size="sm" onClick={handleSkipInsurance}>
              Skip for now
            </Button>
          </div>
          <InsuranceUpdateForm onSubmit={handleInsuranceUpdate} userData={userData} />
        </div>
      )}

      {/* Secondary Insurance Verification Loading */}
      {isVerifyingSecondaryInsurance && (
        <div className="bg-blue-50 border border-blue-100 rounded-lg p-4 flex items-center justify-center h-24">
          <div className="animate-pulse flex flex-col items-center">
            <p className="text-blue-600">Verifying secondary insurance...</p>
          </div>
        </div>
      )}

      {/* Payment Section */}
      {insuranceStatus === "verified" &&
        copayAmount &&
        copayAmount > 0 &&
        !showPaymentForm &&
        !showSecondaryInsuranceForm &&
        !isVerifyingSecondaryInsurance &&
        paymentStatus === "pending" && (
          <div className="mt-6">
            <div className="bg-gray-50 border border-gray-200 rounded-lg p-4 flex items-start gap-3">
              <CreditCard className="text-gray-500 mt-0.5 flex-shrink-0" size={20} />
              <div className="flex-1">
                <p className="text-gray-800 font-medium">Payment Options</p>
                <p className="text-gray-600 text-sm mt-1">
                  You have a copayment for your session. How would you like to take care of it?
                </p>
                <div className="mt-3 flex flex-col gap-2 sm:flex-row">
                  <Button
                    size="sm"
                    onClick={() => setShowPaymentForm(true)}
                    className="bg-[#F7903D] hover:bg-[#e67f2d] text-white"
                  >
                    Add Payment Method
                  </Button>
                  <Button 
                    size="sm" 
                    variant="outline" 
                    onClick={() => setShowSecondaryInsuranceForm(true)}
                    className="border-[#64748B] text-[#64748B]"
                  >
                    Add Secondary Insurance
                  </Button>
                  <Button size="sm" variant="ghost" onClick={handleSkipPayment}>
                    Skip for now
                  </Button>
                </div>
              </div>
            </div>
          </div>
        )}

      {showSecondaryInsuranceForm && (
        <div className="mt-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-medium">Add Secondary Insurance</h3>
            <Button variant="ghost" size="sm" onClick={() => setShowSecondaryInsuranceForm(false)}>
              Cancel
            </Button>
          </div>
          <InsuranceUpdateForm onSubmit={handleSecondaryInsuranceUpdate} userData={userData} />
        </div>
      )}

      {paymentStatus === "skipped" && (
        <div className="bg-amber-50 border border-amber-100 rounded-lg p-4 flex items-start gap-3">
          <AlertCircle className="text-amber-500 mt-0.5 flex-shrink-0" size={20} />
          <div>
            <p className="text-amber-800 font-medium">Payment information skipped</p>
            <p className="text-amber-700 text-sm mt-1">
              You can add your payment information later in your account settings.
            </p>
          </div>
        </div>
      )}

      {showPaymentForm && (
        <div className="mt-6">
          <h3 className="text-lg font-medium mb-4">Payment Information</h3>
          <p className="text-gray-600 mb-4">Your copay amount is ${copayAmount}. Please add a payment method.</p>
          <CreditCardForm onSubmit={handlePaymentSubmit} onSkip={handleSkipPayment} />
        </div>
      )}
    </div>
  )

  return (
    <>
      {/* Desktop Layout */}
      <DesktopLayout title="Appointment Confirmed" subtitle="Your mental health journey begins soon.">
        <div className="bg-white p-8 rounded-lg shadow-sm">
          <MobileStepTracker currentStep={7} totalSteps={7} />
          <div className="mt-8">
            <ConfirmationContent />
          </div>
          {isSetupComplete() && (
            <Button onClick={handleFinish} className="w-full mt-8 bg-[#F7903D] hover:bg-[#e67f2d] text-white h-12">
              Finish Setup
            </Button>
          )}
        </div>
      </DesktopLayout>

      {/* Mobile Layout */}
      <main className="flex min-h-screen flex-col bg-white md:hidden">
        <MobileHeader showBackButton title="Appointment Confirmed" />
        <div className="flex-1 px-4 py-6 pb-36">
          <p className="text-gray-600 mb-6">Your mental health journey begins soon.</p>

          <MobileStepTracker currentStep={7} totalSteps={7} />

          <div className="mt-8">
            <ConfirmationContent />
          </div>
        </div>

        <StickyBottomCTA primaryText="Finish Setup" primaryAction={handleFinish} isDisabled={!isSetupComplete()} />
      </main>
    </>
  )
}
