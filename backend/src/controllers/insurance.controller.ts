import { Request, Response, NextFunction } from 'express';
import { InsuranceCompany } from '../models/InsuranceCompany';

export const getAllInsuranceCompanies = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    console.log('[API] GET /api/insurance-companies called');
    console.log('Current DB:', InsuranceCompany.db.name);
    console.log('Collection name:', InsuranceCompany.collection.name);
    const companies = await InsuranceCompany.find({
      organizationName: { $exists: true, $ne: "" }
    }, {
      organizationName: 1,
      states: 1,
      isMedicaid: 1,
      type: 1,
      tradingPartnerServiceId: 1
    }).sort({ organizationName: 1 });
    console.log(`[API] Found ${companies.length} insurance companies`);
    if (companies.length > 0) {
      console.log('[API] First company:', companies[0]);
    }
    res.status(200).json({ status: 'success', data: companies });
  } catch (error) {
    console.error('[API] Error fetching insurance companies:', error);
    next(error);
  }
};

export const createInsuranceCompany = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const { organizationName, states, isMedicaid, type, tradingPartnerServiceId } = req.body;

    // Validate required fields
    if (!organizationName || !tradingPartnerServiceId) {
      return res.status(400).json({
        status: 'error',
        message: 'Organization name and trading partner service ID are required'
      });
    }

    // Check if company already exists
    const existingCompany = await InsuranceCompany.findOne({ organizationName });
    if (existingCompany) {
      return res.status(400).json({
        status: 'error',
        message: 'Insurance company with this name already exists'
      });
    }

    // Create new insurance company
    const newCompany = await InsuranceCompany.create({
      organizationName,
      states: states || [],
      isMedicaid: isMedicaid || false,
      type: type || '',
      tradingPartnerServiceId
    });

    res.status(201).json({
      status: 'success',
      data: newCompany
    });
  } catch (error) {
    console.error('[API] Error creating insurance company:', error);
    next(error);
  }
}; 