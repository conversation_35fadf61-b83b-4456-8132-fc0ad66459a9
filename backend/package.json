{"name": "lavni-backend", "version": "1.0.0", "description": "Backend for Lavni therapy application", "main": "src/index.ts", "scripts": {"start": "node dist/index.js", "dev": "nodemon src/index.ts", "build": "tsc", "test": "jest"}, "dependencies": {"@types/moment-timezone": "^0.5.30", "axios": "^1.9.0", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.4.5", "express": "^4.18.3", "express-rate-limit": "^7.2.0", "helmet": "^7.1.0", "joi": "^17.12.2", "jsonwebtoken": "^9.0.2", "moment": "^2.30.1", "moment-timezone": "^0.6.0", "mongoose": "^8.2.1", "morgan": "^1.10.0", "winston": "^3.12.0"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jest": "^29.5.12", "@types/jsonwebtoken": "^9.0.6", "@types/morgan": "^1.9.9", "@types/node": "^20.11.24", "@types/react-datepicker": "^6.2.0", "@typescript-eslint/eslint-plugin": "^7.1.1", "@typescript-eslint/parser": "^7.1.1", "eslint": "^8.57.0", "jest": "^29.7.0", "nodemon": "^3.1.10", "ts-jest": "^29.1.2", "ts-node": "^10.9.2", "typescript": "^5.3.3"}}