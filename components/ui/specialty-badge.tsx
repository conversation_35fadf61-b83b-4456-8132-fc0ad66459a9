import React from "react"

interface SpecialtyBadgeProps {
  text: string
  size?: 'sm' | 'md' | 'lg'
  variant?: 'primary' | 'secondary' | 'outline'
}

export function SpecialtyBadge({ text, size = 'md' }: SpecialtyBadgeProps) {
  // Xử lý text dài
  const displayText = text.length > 20 ? `${text.substring(0, 18)}...` : text
  
  const baseClasses = "inline-flex items-center justify-center font-medium transition-all"
  
  const sizeClasses = {
    sm: "text-xs px-2 py-0.5",
    md: "text-xs px-3 py-1",
    lg: "text-sm px-3.5 py-1.5"
  }
  
  // Always use orange color for all badges
  const orangeClasses = "bg-gradient-to-r from-amber-400 to-orange-400 text-gray-800 shadow-sm border border-amber-300"
  
  const shapeClasses = text.length <= 3 
    ? "rounded-md" // Vuông cho text ngắn
    : (text.includes(" ") || text.length > 12) 
      ? "rounded-lg" // Bo tròn nhẹ cho text dài hoặc có khoảng trắng 
      : "rounded-md" // Mặc định
  
  return (
    <span className={`
      ${baseClasses} 
      ${sizeClasses[size]} 
      ${orangeClasses}
      ${shapeClasses}
      hover:shadow-md hover:scale-[1.02] transform duration-200
    `}>
      {displayText}
    </span>
  )
} 