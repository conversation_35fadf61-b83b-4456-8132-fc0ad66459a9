import { addHours, addDays, isWithinInterval, parseISO, format, addMinutes, startOfDay, addDays as dateFnsAddDays } from 'date-fns';
import moment from 'moment-timezone';
import { User } from '../models/User';
import { Appointment } from '../models/Appointment';

interface WorkingHours {
  day: string;
  startTime: string; // Format like "11:30 AM" or "19:30 PM"
  endTime: string;   // Format like "11:30 AM" or "19:30 PM"
}

interface BlockedDate {
  start: Date | string;
  end: Date | string;
}

interface AppointmentType {
  start: Date | string;
  end: Date | string;
}

// Helper function to convert weekday string to number (same as frontend)
const dayOfWeekAsNumber = (day: string): number => {
  const dayMap: Record<string, number> = {
    'Sunday': 0,
    'Monday': 1,
    'Tuesday': 2,
    'Wednesday': 3,
    'Thursday': 4,
    'Friday': 5,
    'Saturday': 6
  };
  return dayMap[day] ?? -1; // Return -1 if day not found
};

// Fetch therapist appointments from local database
const fetchTherapistAppointments = async (therapistId: string): Promise<AppointmentType[]> => {
  try {
    console.log(`[DB] Fetching appointments for therapist ${therapistId}`);
    const appointments = await Appointment.find({
      therapistId,
      status: { $ne: 'CANCELLED' },
      start: { $gte: new Date() }
    }).lean();
    
    console.log(`[DB] Fetched ${appointments.length} appointments from local database`);
    return appointments.map((apt: any) => ({
      start: new Date(apt.start),
      end: new Date(apt.end)
    }));
  } catch (error: any) {
    console.log(`[DB] Failed to fetch appointments from local database:`, error.message);
    return [];
  }
};

// Fetch therapist blocked dates from local database
const fetchTherapistBlockedDates = async (therapistId: string): Promise<BlockedDate[]> => {
  try {
    console.log(`[DB] Fetching blocked dates for therapist ${therapistId}`);
    const therapist = await User.findById(therapistId).lean();
    
    if (!therapist || !therapist.blockedDates) {
      console.log(`[DB] No blocked dates found for therapist ${therapistId}`);
      return [];
    }
    
    const blockedDates = therapist.blockedDates.map((blocked: any) => ({
      start: new Date(blocked.start),
      end: new Date(blocked.end)
    }));
    
    console.log(`[DB] Fetched ${blockedDates.length} blocked dates from local database`);
    return blockedDates;
  } catch (error: any) {
    console.log(`[DB] Failed to fetch blocked dates from local database:`, error.message);
    return [];
  }
};

export async function calculateNextAvailability(
  therapistId: string,
  workingHours: WorkingHours[]
): Promise<string | null> {
  console.log('[AVAILABILITY] Starting calculation with:', {
    therapistId,
    workingHours: JSON.stringify(workingHours)
  });

  // Check if working hours are empty or malformed
  if (!workingHours || workingHours.length === 0) {
    console.log(`[AVAILABILITY] No working hours defined for therapist ${therapistId}`);
    return null;
  }

  const validWorkingHours = workingHours.filter(wh => wh.day && wh.startTime && wh.endTime);
  if (validWorkingHours.length === 0) {
    console.log(`[AVAILABILITY] No valid working hours found for therapist ${therapistId}. Original data:`, workingHours);
    return null;
  }

  console.log(`[AVAILABILITY] Found ${validWorkingHours.length} valid working hour entries for therapist ${therapistId}`);

  // Fetch appointments and blocked dates from local database
  const [appointments, blockedDates] = await Promise.all([
    fetchTherapistAppointments(therapistId),
    fetchTherapistBlockedDates(therapistId)
  ]);

  console.log(`[AVAILABILITY] Fetched ${appointments.length} appointments and ${blockedDates.length} blocked dates`);

  // Process working hours - fix parsing for UTC format like "13:30 PM" (should be treated as UTC then converted to Eastern)
  const availableDays: any[] = [];
  
  validWorkingHours.forEach((obj: WorkingHours) => {
    const dayAsNumber = dayOfWeekAsNumber(obj.day);
    
    if (dayAsNumber === -1) {
      console.log(`[AVAILABILITY] Invalid day name: ${obj.day}`);
      return;
    }

    // Parse UTC time and convert to Eastern Time (handles daylight saving automatically)
    const parseUTCTimeToEastern = (timeStr: string): string => {
      // Remove PM/AM suffix and treat as 24h UTC format
      const cleanTime = timeStr.replace(/\s*(PM|AM|pm|am)\s*$/, '');
      
      // Validate format HH:MM
      if (!/^\d{1,2}:\d{2}$/.test(cleanTime)) {
        console.log(`[AVAILABILITY] Invalid time format: ${timeStr} -> ${cleanTime}`);
        return timeStr; // fallback to original
      }
      
      // Create a UTC moment and convert to Eastern time
      const [hours, minutes] = cleanTime.split(':').map(Number);
      const utcMoment = moment.utc().hours(hours).minutes(minutes).seconds(0);
      const easternMoment = utcMoment.tz('America/New_York');
      
      console.log(`[AVAILABILITY] Converting ${timeStr} (${cleanTime} UTC) to Eastern: ${easternMoment.format('HH:mm')}`);
      
      return easternMoment.format('HH:mm');
    };

    const startTime = parseUTCTimeToEastern(obj.startTime);
    const endTime = parseUTCTimeToEastern(obj.endTime);
    
    console.log(`[AVAILABILITY] Parsed working hours for ${obj.day}: ${obj.startTime} -> ${startTime}, ${obj.endTime} -> ${endTime}`);
    
    // Compare start and end times to detect overnight shifts
    const [startHour, startMin] = startTime.split(':').map(Number);
    const [endHour, endMin] = endTime.split(':').map(Number);
    const startTotalMin = startHour * 60 + startMin;
    const endTotalMin = endHour * 60 + endMin;
    
    if (endTotalMin > startTotalMin) {
      // Normal case: same day
      availableDays.push({
        startTime: startTime,
        endTime: endTime,
        daysOfWeek: [dayAsNumber],
      });
    } else {
      // Overnight shift: split into two periods
      availableDays.push({
        startTime: startTime,
        endTime: "23:59",
        daysOfWeek: [dayAsNumber],
      });

      availableDays.push({
        startTime: "00:00",
        endTime: endTime,
        daysOfWeek: [(dayAsNumber + 1) % 7],
      });
    }
  });

  console.log(`[AVAILABILITY] Processed working hours into ${availableDays.length} day periods:`, availableDays);

  // Calculate available dates for next 60 days
  const today = new Date();
  const tomorrowDate = moment(today).add(1, "day").startOf("day").toDate();

  // Check each day for the next 60 days
  for (let dayOffset = 0; dayOffset < 60; dayOffset++) {
    const checkDate = moment(tomorrowDate).add(dayOffset, 'days').toDate();
    const dayOfWeek = checkDate.getDay();

    console.log(`\n[AVAILABILITY] === Checking ${checkDate.toDateString()} (day ${dayOfWeek}) ===`);

    // Filter appointments and blocked dates for this specific date
    const dateStr = moment(checkDate).format('YYYY-MM-DD');
    
    const dayAppointments = appointments.filter((apt: any) => {
      const aptStart = moment(apt.start);
      return aptStart.format('YYYY-MM-DD') === dateStr;
    });

    const dayBlockedDates = blockedDates.filter((blocked: any) => {
      const blockStart = moment(blocked.start);
      const blockEnd = moment(blocked.end);
      return moment(checkDate).isBetween(blockStart, blockEnd, 'day', '[]');
    });

    console.log(`[AVAILABILITY] Found ${dayAppointments.length} appointments and ${dayBlockedDates.length} blocked periods for this day`);

    // Get available hours for this day of week
    const dayAvailableHours = availableDays.filter(obj => obj.daysOfWeek[0] === dayOfWeek);
    
    if (dayAvailableHours.length === 0) {
      console.log(`[AVAILABILITY] No working hours for day ${dayOfWeek}`);
      continue;
    }

    console.log(`[AVAILABILITY] Working hours for this day:`, dayAvailableHours);

    // Generate time slots for this day
    let allAvailableSlots: string[] = [];

    dayAvailableHours.forEach((availableHour: any) => {
      const [startHour, startMin] = availableHour.startTime.split(':').map(Number);
      const [endHour, endMin] = availableHour.endTime.split(':').map(Number);

      console.log(`[AVAILABILITY] Processing period: ${startHour}:${startMin} - ${endHour}:${endMin}`);

      // Generate hourly slots
      for (let hr = startHour; hr <= endHour; hr++) {
        // For start hour, only add if startTime minute is 0
        if (hr === startHour) {
          if (startMin === 0) {
            const timeSlot = moment().hour(hr).minute(0).format("h:mm A");
            allAvailableSlots.push(timeSlot);
          }
        } else {
          // For non-start hours, always add the hour slot
          const timeSlot = moment().hour(hr).minute(0).format("h:mm A");
          allAvailableSlots.push(timeSlot);
        }

        // Add 30-minute slot if not the end hour, or if end hour has 30 minutes
        if (hr !== endHour) {
          const timeSlot = moment().hour(hr).minute(30).format("h:mm A");
          allAvailableSlots.push(timeSlot);
        } else {
          if (endMin >= 30) {
            const timeSlot = moment().hour(hr).minute(30).format("h:mm A");
            allAvailableSlots.push(timeSlot);
          }
        }
      }
    });

    // Remove duplicate slots
    allAvailableSlots = [...new Set(allAvailableSlots)];
    console.log(`[AVAILABILITY] Generated ${allAvailableSlots.length} potential slots:`, allAvailableSlots);

    // Filter slots to ensure each slot has a full 1-hour duration within working hours
    const validSlots = allAvailableSlots.filter(slot => {
      const slotTime = moment(slot, "h:mm A");
      const slotEndTime = moment(slotTime).add(60, "minutes");

      const slotStartInWorkingHours = allAvailableSlots.includes(slotTime.format("h:mm A"));
      const slotEndInWorkingHours = allAvailableSlots.includes(slotEndTime.format("h:mm A"));

      return slotStartInWorkingHours && slotEndInWorkingHours;
    });

    console.log(`[AVAILABILITY] After 1-hour validation: ${validSlots.length} slots:`, validSlots);

    // Filter out blocked and booked slots
    const availableSlots = validSlots.filter(slot => {
      const slotTime = moment(slot, "h:mm A");
      const slotDateTime = moment(checkDate)
        .hours(slotTime.hours())
        .minutes(slotTime.minutes())
        .seconds(0);
      const slotEndTime = slotDateTime.clone().add(1, 'hour'); // 1-hour appointment duration

      // Check if slot overlaps with blocked dates
      const isBlocked = dayBlockedDates.some((blocked: any) => {
        const blockStart = moment(blocked.start);
        const blockEnd = moment(blocked.end);
        
        // Check if slot overlaps with blocked time
        // Overlap if: slotStart < blockEnd AND slotEnd > blockStart
        const hasOverlap = slotDateTime.isBefore(blockEnd) && slotEndTime.isAfter(blockStart);
        
        if (hasOverlap) {
          console.log(`[AVAILABILITY] Slot ${slot} (${slotDateTime.format('HH:mm')}-${slotEndTime.format('HH:mm')}) blocked by: ${blockStart.format('YYYY-MM-DD HH:mm:ss')} - ${blockEnd.format('YYYY-MM-DD HH:mm:ss')}`);
        }
        return hasOverlap;
      });

      if (isBlocked) {
        return false;
      }

      // Check if slot overlaps with appointments
      const hasAppointment = dayAppointments.some((appointment: any) => {
        const aptStart = moment(appointment.start);
        const aptEnd = moment(appointment.end);
        
        // Check if slot overlaps with appointment time
        // Overlap if: slotStart < aptEnd AND slotEnd > aptStart
        const hasOverlap = slotDateTime.isBefore(aptEnd) && slotEndTime.isAfter(aptStart);
        
        if (hasOverlap) {
          console.log(`[AVAILABILITY] Slot ${slot} (${slotDateTime.format('HH:mm')}-${slotEndTime.format('HH:mm')}) has appointment: ${aptStart.format('YYYY-MM-DD HH:mm:ss')} - ${aptEnd.format('YYYY-MM-DD HH:mm:ss')}`);
        }
        return hasOverlap;
      });

      const isAvailable = !hasAppointment;
      console.log(`[AVAILABILITY] Slot ${slot} final result: available=${isAvailable}`);
      return isAvailable;
    });

    console.log(`[AVAILABILITY] Final available slots for ${dateStr}: ${availableSlots.length} slots:`, availableSlots);

    // If this day has available slots, return the first one
    if (availableSlots.length > 0) {
      const nextAvailableSlot = availableSlots[0];
      
      // Format the result
      const dayOfWeek = format(checkDate, 'EEEE');
      const monthDay = format(checkDate, 'MMMM do');
      
      const formattedAvailability = `${dayOfWeek}, ${monthDay} at ${nextAvailableSlot}`;
      console.log(`[AVAILABILITY] ✅ FOUND NEXT AVAILABILITY: ${formattedAvailability}`);
      
      return formattedAvailability;
    }
  }

  console.log(`[AVAILABILITY] ❌ No available slots found for therapist ${therapistId} in the next 60 days`);
  return null;
}