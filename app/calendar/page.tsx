"use client"

import { useRouter } from "next/navigation"
import { useState, useEffect } from "react"
import { StickyBottomCTA } from "@/components/sticky-bottom-cta"
import { MobileStepTracker } from "@/components/mobile-step-tracker"
import { MobileCalendarSelector } from "@/components/mobile-calendar-selector"
import { DesktopLayout } from "@/components/desktop-layout"
import { MobileHeader } from "@/components/mobile-header"
import { Button } from "@/components/ui/button"
import { ChevronLeft } from "lucide-react"

export default function CalendarPage() {
  const router = useRouter()
  const [selectedTherapist, setSelectedTherapist] = useState<any>(null)
  const [selectedDate, setSelectedDate] = useState<Date | null>(null)
  const [selectedTimeSlot, setSelectedTimeSlot] = useState<string | null>(null)

  useEffect(() => {
    // Retrieve selected therapist from localStorage
    const storedTherapist = localStorage.getItem("selectedTherapist")
    if (storedTherapist) {
      const therapist = JSON.parse(storedTherapist)
      console.log('[DEBUG] Selected therapist from localStorage:', therapist)
      console.log('[DEBUG] Therapist ID fields:', {
        _id: therapist._id,
        id: therapist.id,
        therapistId: therapist.therapistId
      })
      setSelectedTherapist(therapist)
    } else {
      console.log('[DEBUG] No therapist found in localStorage')
    }
  }, [])

  const handleConfirmAppointment = () => {
    if (selectedDate && selectedTimeSlot) {
      // Store appointment details
      localStorage.setItem(
        "appointmentDetails",
        JSON.stringify({
          therapistId: selectedTherapist?.id,
          date: selectedDate.toISOString(),
          timeSlot: selectedTimeSlot,
        }),
      )

      router.push("/who-is-this-for")
    }
  }

  return (
    <>
      {/* Desktop Layout */}
      <DesktopLayout
        title="Select Appointment Time"
        subtitle={selectedTherapist ? `Choose a time to meet with ${selectedTherapist.name}` : ""}
      >
        <div className="bg-white p-8 rounded-lg shadow-sm">
          <MobileStepTracker currentStep={3} totalSteps={7} />

          {selectedTherapist && (
            <MobileCalendarSelector
              therapistName={selectedTherapist.name}
              onDateSelect={setSelectedDate}
              onTimeSelect={setSelectedTimeSlot}
              selectedDate={selectedDate}
              selectedTimeSlot={selectedTimeSlot}
            />
          )}

          <div className="flex gap-3 mt-8">
            <Button variant="outline" onClick={() => router.back()} className="flex-1 border-[#64748B] text-[#64748B]">
              <ChevronLeft size={16} className="mr-1" />
              Back
            </Button>
            <Button
              onClick={handleConfirmAppointment}
              disabled={!selectedDate || !selectedTimeSlot}
              className="flex-1 bg-[#F7903D] hover:bg-[#e67f2d] text-white"
            >
              Confirm Appointment
            </Button>
          </div>
        </div>
      </DesktopLayout>

      {/* Mobile Layout */}
      <main className="flex min-h-screen flex-col bg-white md:hidden">
        <MobileHeader showBackButton title="Select Appointment Time" />
        <div className="flex-1 px-4 py-6 pb-36">
          {selectedTherapist && (
            <p className="text-gray-600 mb-6">Choose a time to meet with {selectedTherapist.name}</p>
          )}

          <MobileStepTracker currentStep={3} totalSteps={7} />

          {selectedTherapist && (
            <MobileCalendarSelector
              therapistName={selectedTherapist.name}
              onDateSelect={setSelectedDate}
              onTimeSelect={setSelectedTimeSlot}
              selectedDate={selectedDate}
              selectedTimeSlot={selectedTimeSlot}
            />
          )}
        </div>

        <StickyBottomCTA
          primaryText="Confirm Appointment"
          primaryAction={handleConfirmAppointment}
          secondaryText="Back"
          secondaryAction={() => router.back()}
          isDisabled={!selectedDate || !selectedTimeSlot}
        />
      </main>
    </>
  )
}
