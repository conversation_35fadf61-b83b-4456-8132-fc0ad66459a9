import { Request, Response } from 'express';
import { User } from '../models/User';
import { Appointment } from '../models/Appointment';
import { calculateNextAvailability } from '../utils/availability';
import { addDays, format, startOfMonth, endOfMonth, isSameDay } from 'date-fns';

// Get all therapists
export const getAllTherapists = async (req: Request, res: Response) => {
  try {
    const therapists = await User.find({ 
      role: 'THERAPIST',
      adminApproved: true 
    }).select('-password -email -role -createdAt -updatedAt -__v');

    // For each therapist, calculate nextAvailability
    const enhancedTherapists = await Promise.all(therapists.map(async (t: any) => {
      // Normalize working hours to match slot logic
      const workingHours = (t.workingHours || []).map((wh: any) => ({
        day: wh.day,
        startTime: convertToISOTime(wh.startTime),
        endTime: convertToISOTime(wh.endTime),
      }));
      const blockedDates = t.blockedDates || [];
      // Get all future appointments
      const appointments = await Appointment.find({
        therapistId: t._id,
        status: { $ne: 'CANCELLED' },
        start: { $gte: new Date() }
      });
      
      // Normalize working hours to match frontend logic
      const normalizedWorkingHours = Array.isArray(t.workingHours)
        ? t.workingHours.map((wh: any) => ({
            day: wh.day || '',
            startTime: wh.startTime || '',
            endTime: wh.endTime || '',
          }))
        : [];
        
      const nextAvailability = await calculateNextAvailability(
        t._id.toString(),
        normalizedWorkingHours
      );
      return {
        ...t.toObject(),
        nextAvailability,
      };
    }));

    res.status(200).json(enhancedTherapists);
  } catch (error) {
    console.error('Error fetching therapists:', error);
    res.status(500).json({ message: 'Error fetching therapists' });
  }
};

// Helper to convert 12/24 hour time to ISO string for slot logic
function convertToISOTime(timeStr: string): string {
  // If already ISO, return as is
  if (/^\d{2}:\d{2}$/.test(timeStr)) return `1970-01-01T${timeStr}:00.000Z`;
  // Handle 12-hour format
  let [time, modifier] = timeStr.split(/\s+/);
  let [hour, minute] = time.split(':').map(Number);
  if (modifier) {
    if (modifier.toUpperCase() === 'PM' && hour < 12) hour += 12;
    if (modifier.toUpperCase() === 'AM' && hour === 12) hour = 0;
  }
  return `1970-01-01T${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}:00.000Z`;
}

// Get therapist by ID
export const getTherapistById = async (req: Request, res: Response) => {
  try {
    const therapist = await User.findOne({ 
      _id: req.params.id,
      role: 'therapist',
      adminApproved: true 
    }).select('-password -email -role -createdAt -updatedAt -__v');

    if (!therapist) {
      return res.status(404).json({ message: 'Therapist not found' });
    }

    res.status(200).json(therapist);
  } catch (error) {
    console.error('Error fetching therapist:', error);
    res.status(500).json({ message: 'Error fetching therapist' });
  }
};

// Get available days for a therapist in a month
export const getAvailableDays = async (req: Request, res: Response) => {
  try {
    const therapistId = req.params.id;
    const { month } = req.query; // month: 'YYYY-MM'
    if (!month) return res.status(400).json({ message: 'Month is required' });
    
    console.log('[BACKEND] Getting available days for month:', month);
    
    const therapist = await User.findById(therapistId);
    if (!therapist) return res.status(404).json({ message: 'Therapist not found' });
    
    const workingHours = therapist.workingHours || [];
    console.log('[BACKEND] Raw working hours:', JSON.stringify(workingHours));
    
    // Normalize working hours
    function to24Hour(timeStr: string) {
      let [time, modifier] = timeStr.split(/\s+/);
      let [hour, minute] = time.split(':').map(Number);
      if (modifier) {
        if (modifier.toUpperCase() === 'PM' && hour < 12) hour += 12;
        if (modifier.toUpperCase() === 'AM' && hour === 12) hour = 0;
      }
      return `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;
    }
    
    const normalized = workingHours.map((w: any) => ({
      day: w.day.toLowerCase(),
      startTime: to24Hour(w.startTime),
      endTime: to24Hour(w.endTime),
    }));
    
    console.log('[BACKEND] Normalized working hours:', JSON.stringify(normalized));
    
    // Parse year and month explicitly to avoid timezone issues
    const [year, monthNum] = (month as string).split('-').map(Number);
    const start = startOfMonth(new Date(year, monthNum - 1, 1)); // monthNum - 1 because JS months are 0-indexed
    const end = endOfMonth(start);
    const days: string[] = [];
    
    console.log('[BACKEND] Month parameter:', month);
    console.log('[BACKEND] Parsed year:', year, 'month:', monthNum);
    console.log('[BACKEND] Start date:', start.toISOString());
    console.log('[BACKEND] End date:', end.toISOString());
    
    // Get all appointments for the month
    const appointments = await Appointment.find({
      therapistId,
      status: { $ne: 'CANCELLED' },
      start: { $gte: start }
    });
    
    // Get all blocked dates
    const blockedDates = therapist.blockedDates || [];
    
    console.log('[BACKEND] Found appointments:', appointments.length);
    console.log('[BACKEND] Found blocked dates:', blockedDates.length);
    
    // For each day in the month
    for (let d = new Date(start); d <= end; d = addDays(d, 1)) {
      const dayOfWeek = d.toLocaleDateString('en-US', { weekday: 'long' }).toLowerCase();
      console.log(`\n[BACKEND] Checking date: ${format(d, 'yyyy-MM-dd')} (${dayOfWeek})`);
      
      // Check if this day has working hours
      const wh = normalized.filter((w: any) => w.day === dayOfWeek);
      if (!wh.length) {
        console.log(`[BACKEND] No working hours for ${dayOfWeek}`);
        continue;
      }
      
      console.log(`[BACKEND] Found working hours for ${dayOfWeek}:`, JSON.stringify(wh));
      
      // Check if the day is blocked
      const isBlocked = blockedDates.some((blocked: any) => {
        const blockedStart = new Date(blocked.start);
        const blockedEnd = new Date(blocked.end);
        return d >= blockedStart && d <= blockedEnd;
      });
      
      if (isBlocked) {
        console.log(`[BACKEND] Day is blocked`);
        continue;
      }
      
      // Check if there are any appointments on this day
      const hasAppointments = appointments.some((appt: any) => {
        const apptDate = new Date(appt.start);
        return isSameDay(d, apptDate);
      });
      
      if (hasAppointments) {
        console.log(`[BACKEND] Day has appointments`);
        // We still want to show this day, just with limited slots
      }
      
      // If we get here, the day has working hours and isn't completely blocked
      // Add it to available days
      days.push(format(d, 'yyyy-MM-dd'));
      console.log(`[BACKEND] Added day as available`);
    }
    
    console.log('[BACKEND] Final available days:', days);
    res.json({ days });
  } catch (error) {
    console.error('Error fetching available days:', error);
    res.status(500).json({ message: 'Error fetching available days' });
  }
};

// Get available slots for a therapist on a specific day
export const getAvailableSlots = async (req: Request, res: Response) => {
  try {
    const therapistId = req.params.id;
    const date = req.query.date as string;
    if (!date) return res.status(400).json({ message: 'Date is required' });
    
    console.log('[BACKEND] Getting slots for date:', date);
    
    const therapist = await User.findById(therapistId);
    if (!therapist) return res.status(404).json({ message: 'Therapist not found' });
    
    const appointments = await Appointment.find({
      therapistId,
      status: { $ne: 'CANCELLED' },
      start: { $gte: new Date(date + 'T00:00:00Z'), $lt: new Date(date + 'T23:59:59Z') }
    });
    
    const blockedDates = therapist.blockedDates || [];
    const workingHours = therapist.workingHours || [];
    // Parse the date as UTC to get the correct day of week
    const [year, month, day] = date.split('-').map(Number);
    const d = new Date(Date.UTC(year, month - 1, day));
    const dayOfWeek = d.toLocaleDateString('en-US', { weekday: 'long', timeZone: 'UTC' });
    
    console.log('[BACKEND] Processing for day:', dayOfWeek);
    console.log('[BACKEND] Raw working hours:', JSON.stringify(workingHours));
    
    const wh = workingHours.filter((wh: any) => wh.day === dayOfWeek);
    if (!wh.length) {
      console.log('[BACKEND] No working hours for this day');
      return res.json({ slots: [] });
    }
    
    function to24Hour(timeStr: string) {
      // Handle case where time is already in 24h format
      if (!timeStr.includes('AM') && !timeStr.includes('PM')) {
        return timeStr;
      }
      
      let [time, modifier] = timeStr.split(/\s+/);
      let [hour, minute] = time.split(':').map(Number);
      
      // Handle 12-hour format conversion
      if (modifier) {
        if (modifier.toUpperCase() === 'PM' && hour < 12) hour += 12;
        if (modifier.toUpperCase() === 'AM' && hour === 12) hour = 0;
      }
      
      return `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;
    }
    
    const normalized = wh.map((w: any) => ({
      day: w.day,
      startTime: to24Hour(w.startTime),
      endTime: to24Hour(w.endTime),
    }));
    
    console.log('[BACKEND] Normalized working hours:', JSON.stringify(normalized));
    
    const slots: string[] = [];
    for (const w of normalized) {
      const slotStart = new Date(d);
      const [startHour, startMinute] = w.startTime.split(':').map(Number);
      slotStart.setHours(startHour, startMinute, 0, 0);
      const slotEnd = new Date(d);
      const [endHour, endMinute] = w.endTime.split(':').map(Number);
      slotEnd.setHours(endHour, endMinute, 0, 0);
      // Handle overnight slots
      if (slotEnd <= slotStart) {
        slotEnd.setDate(slotEnd.getDate() + 1);
      }
      console.log('[BACKEND] Processing slot range:', {
        start: slotStart.toISOString(),
        end: slotEnd.toISOString()
      });
      let currentSlot = new Date(slotStart);
      while (currentSlot < slotEnd) {
        const slotEndTime = new Date(currentSlot);
        slotEndTime.setHours(currentSlot.getHours() + 1);
        if (slotEndTime <= slotEnd) {
          if (currentSlot > new Date()) {
            // Debug: print slot and slotEndTime
            console.log('[DEBUG] Checking slot:', {
              slot: currentSlot.toISOString(),
              slotEnd: slotEndTime.toISOString()
            });
            // Check blocked
            let blocked = false;
            for (const blockedItem of blockedDates) {
              const blockedStart = new Date(blockedItem.start);
              const blockedEnd = new Date(blockedItem.end);
              
              // Check if slot overlaps with blocked time
              // Overlap if: slotStart < blockEnd AND slotEnd > blockStart
              const hasOverlap = currentSlot < blockedEnd && slotEndTime > blockedStart;
              
              console.log('[DEBUG] Blocked check:', {
                slot: currentSlot.toISOString(),
                slotEnd: slotEndTime.toISOString(),
                blockedStart: blockedStart.toISOString(),
                blockedEnd: blockedEnd.toISOString(),
                hasOverlap
              });
              if (hasOverlap) blocked = true;
            }
            // Check appointments
            let hasAppt = false;
            for (const appt of appointments) {
              const apptStart = new Date(appt.start);
              const apptEnd = new Date(appt.end);
              
              // Check if slot overlaps with appointment time
              // Overlap if: slotStart < aptEnd AND slotEnd > aptStart
              const hasOverlap = currentSlot < apptEnd && slotEndTime > apptStart;
              
              console.log('[DEBUG] Appointment check:', {
                slot: currentSlot.toISOString(),
                slotEnd: slotEndTime.toISOString(),
                apptStart: apptStart.toISOString(),
                apptEnd: apptEnd.toISOString(),
                hasOverlap
              });
              if (hasOverlap) hasAppt = true;
            }
            if (!blocked && !hasAppt) {
              const slotISO = currentSlot.toISOString();
              console.log('[BACKEND] Adding available slot:', {
                slot: slotISO,
                localTime: currentSlot.toLocaleTimeString('en-US', { hour: 'numeric', minute: '2-digit', hour12: true })
              });
              slots.push(slotISO);
            } else {
              console.log('[DEBUG] Slot filtered out:', {
                slot: currentSlot.toISOString(),
                blocked,
                hasAppt
              });
            }
          }
        }
        currentSlot = new Date(slotEndTime);
      }
    }
    
    console.log('[BACKEND] Final available slots:', slots.map(slot => ({
      iso: slot,
      localTime: new Date(slot).toLocaleTimeString('en-US', { hour: 'numeric', minute: '2-digit', hour12: true })
    })));
    res.json({ slots });
  } catch (error) {
    console.error('Error fetching available slots:', error);
    res.status(500).json({ message: 'Error fetching available slots' }); 
  }
};

// Test endpoint to debug therapist data
export const testTherapistData = async (req: Request, res: Response) => {
  try {
    // Allow passing therapistId as query parameter, default to Marcus King
    const therapistId = req.query.therapistId as string || '62f17eb1b81f31a68505d9ad';
    
    console.log(`\n=== DEBUGGING THERAPIST ${therapistId} ===`);
    
    // 1. Get therapist details
    const therapist = await User.findById(therapistId);
    if (!therapist) {
      return res.status(404).json({ message: 'Therapist not found' });
    }
    
    console.log('1. THERAPIST BASIC INFO:');
    console.log(`   Name: ${therapist.firstname} ${therapist.lastname}`);
    console.log(`   Role: ${therapist.role}`);
    console.log(`   Admin Approved: ${therapist.adminApproved}`);
    
    // 2. Working Hours
    console.log('\n2. WORKING HOURS:');
    const workingHours = therapist.workingHours || [];
    console.log(`   Count: ${workingHours.length}`);
    workingHours.forEach((wh: any, index: number) => {
      console.log(`   ${index + 1}. Day: ${wh.day}, Start: ${wh.startTime}, End: ${wh.endTime}`);
    });
    
    // 3. Blocked Dates
    console.log('\n3. BLOCKED DATES:');
    const blockedDates = therapist.blockedDates || [];
    console.log(`   Count: ${blockedDates.length}`);
    blockedDates.forEach((bd: any, index: number) => {
      console.log(`   ${index + 1}. Start: ${bd.start}, End: ${bd.end}`);
    });
    
    // 4. Appointments
    console.log('\n4. APPOINTMENTS:');
    const appointments = await Appointment.find({
      therapistId: therapistId,
      status: { $ne: 'CANCELLED' },
      start: { $gte: new Date() }
    });
    console.log(`   Count: ${appointments.length}`);
    appointments.forEach((apt: any, index: number) => {
      console.log(`   ${index + 1}. Start: ${apt.start}, End: ${apt.end}, Status: ${apt.status}`);
    });
    
    // 5. Calculate next availability using current backend logic
    console.log('\n5. CALCULATING NEXT AVAILABILITY...');
    const normalizedWorkingHours = workingHours.map((wh: any) => ({
      day: wh.day || '',
      startTime: wh.startTime || '',
      endTime: wh.endTime || '',
    }));
    
    const { calculateNextAvailability } = await import('../utils/availability');
    const nextAvailability = await calculateNextAvailability(therapistId, normalizedWorkingHours);
    console.log(`   Next Availability: ${nextAvailability}`);
    
    console.log('\n=== END DEBUG ===\n');
    
    // Return JSON response
    res.json({
      therapistId,
      therapistName: `${therapist.firstname} ${therapist.lastname}`,
      workingHours: workingHours,
      blockedDates: blockedDates,
      appointments: appointments.map((apt: any) => ({
        start: apt.start,
        end: apt.end,
        status: apt.status
      })),
      nextAvailability,
      debug: 'Check console for detailed logs'
    });
    
  } catch (error) {
    console.error('Error in testTherapistData:', error);
    res.status(500).json({ 
      message: 'Error debugging therapist data', 
      error: error instanceof Error ? error.message : String(error)
    });
  }
}; 