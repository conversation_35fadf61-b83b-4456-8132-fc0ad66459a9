import { MongoClient } from 'mongodb';
import dotenv from 'dotenv';

dotenv.config();

const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017';
const DB_NAME = process.env.DB_NAME || 'lavni';

// This mapping should be populated with actual trading partner service IDs from Claim.MD
const insuranceCompanyMapping: { [key: string]: string } = {
  'Aetna': 'AETNA',
  'Anthem': 'ANTHEM',
  'Blue Cross Blue Shield': 'BCBS',
  'Cigna': 'CIGNA',
  'Humana': 'HUMANA',
  'UnitedHealthcare': 'UHC',
  // Add more mappings as needed
};

async function updateInsuranceCompanies() {
  const client = new MongoClient(MONGODB_URI);

  try {
    await client.connect();
    console.log('Connected to MongoDB');

    const db = client.db(DB_NAME);
    const collection = db.collection('insurancecompanies');

    // Get all insurance companies
    const insuranceCompanies = await collection.find({}).toArray();
    console.log(`Found ${insuranceCompanies.length} insurance companies`);

    // Update each insurance company with its trading partner service ID
    for (const company of insuranceCompanies) {
      const tradingPartnerServiceId = insuranceCompanyMapping[company.organizationName];
      
      if (tradingPartnerServiceId) {
        await collection.updateOne(
          { _id: company._id },
          { $set: { tradingPartnerServiceId } }
        );
        console.log(`Updated ${company.organizationName} with trading partner service ID: ${tradingPartnerServiceId}`);
      } else {
        console.warn(`No trading partner service ID found for ${company.organizationName}`);
      }
    }

    console.log('Update completed');
  } catch (error) {
    console.error('Error updating insurance companies:', error);
  } finally {
    await client.close();
    console.log('Disconnected from MongoDB');
  }
}

// Run the update
updateInsuranceCompanies().catch(console.error); 