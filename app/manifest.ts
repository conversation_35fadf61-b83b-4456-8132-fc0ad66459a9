import type { MetadataRoute } from "next"

export default function manifest(): MetadataRoute.Manifest {
  return {
    name: "Lavni Mental Health",
    short_name: "<PERSON><PERSON><PERSON>",
    description: "Mental health platform for Medicaid clients",
    start_url: "/",
    display: "standalone",
    background_color: "#ffffff",
    theme_color: "#F7903D",
    icons: [
      {
        src: "/icon-192x192.svg",
        sizes: "192x192",
        type: "image/svg+xml",
      },
      {
        src: "/icon-512x512.svg",
        sizes: "512x512",
        type: "image/svg+xml",
      },
    ],
  }
}
