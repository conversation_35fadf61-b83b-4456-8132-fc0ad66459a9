/** @type {import('next').NextConfig} */
const nextConfig = {
  images: {
    domains: ['api.lavnihealth.com'],
  },
  async rewrites() {
    const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3003';
    return [
      {
        source: '/api/insurance-companies',
        destination: `${apiUrl}/api/insurance-companies`,
      },
      {
        source: '/api/therapists',
        destination: `${apiUrl}/api/users/therapists`,
      },
      {
        source: '/api/therapists/:path*',
        destination: `${apiUrl}/api/therapists/:path*`,
      },
    ];
  },
};

module.exports = nextConfig; 