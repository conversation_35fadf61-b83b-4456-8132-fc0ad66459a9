import dotenv from 'dotenv';

dotenv.config();

interface Config {
  port: number;
  mongoUri: string;
  jwtSecret: string;
  corsOrigin: string;
  environment: string;
}

export const config: Config = {
  port: parseInt(process.env.PORT || '3003', 10),
  mongoUri: process.env.MONGODB_URI || 'mongodb://localhost:27017/lavni',
  jwtSecret: process.env.JWT_SECRET || 'your-secret-key',
  corsOrigin: process.env.CORS_ORIGIN || 'http://localhost:3000',
  environment: process.env.NODE_ENV || 'development',
}; 