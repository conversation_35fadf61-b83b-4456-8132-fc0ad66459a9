@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --primary: #f7903d;
  --accent: #fec07e;
  --secondary: #64748b;
  --background: #ffffff;
  --foreground: #0f172a;
}

body {
  color: var(--foreground);
  background: var(--background);
  -webkit-tap-highlight-color: transparent;
  overscroll-behavior: none;
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
  input,
  select,
  textarea {
    @apply min-h-11 text-base;
  }
}

@layer components {
  .page-transition-enter {
    opacity: 0;
    transform: translateX(100%);
  }

  .page-transition-enter-active {
    opacity: 1;
    transform: translateX(0);
    transition: opacity 300ms, transform 300ms;
  }

  .page-transition-exit {
    opacity: 1;
    transform: translateX(0);
  }

  .page-transition-exit-active {
    opacity: 0;
    transform: translateX(-100%);
    transition: opacity 300ms, transform 300ms;
  }

  .modal-enter {
    opacity: 0;
    transform: scale(0.95);
  }

  .modal-enter-active {
    opacity: 1;
    transform: scale(1);
    transition: opacity 200ms, transform 200ms;
  }

  .modal-exit {
    opacity: 1;
    transform: scale(1);
  }

  .modal-exit-active {
    opacity: 0;
    transform: scale(0.95);
    transition: opacity 200ms, transform 200ms;
  }
}
