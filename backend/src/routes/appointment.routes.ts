import { Router } from 'express';
import {
  createAppointment,
  getAppointments,
  getAppointmentById,
  updateAppointment,
  deleteAppointment,
  getTherapistAppointments,
  getClientAppointments
} from '../controllers/appointment.controller';
import { authenticate } from '../middleware/auth.middleware';
import { validateAppointment } from '../middleware/validation.middleware';

const router = Router();

// All appointment routes require authentication
router.use(authenticate);

// Appointment routes
router.post('/', validateAppointment, createAppointment);
router.get('/', getAppointments);
router.get('/:id', getAppointmentById);
router.put('/:id', validateAppointment, updateAppointment);
router.delete('/:id', deleteAppointment);

// Specialized appointment routes
router.get('/therapist/:therapistId', getTherapistAppointments);
router.get('/client/:clientId', getClientAppointments);

export default router; 