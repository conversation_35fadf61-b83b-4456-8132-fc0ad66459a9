"use client"

import { useRouter } from "next/navigation"
import { useState, useEffect } from "react"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"
import { StickyBottomCTA } from "@/components/sticky-bottom-cta"
import { MobileStepTracker } from "@/components/mobile-step-tracker"
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Button } from "@/components/ui/button"
import { GoogleOAuthButton } from "@/components/google-oauth-button"
import { Eye, EyeOff } from "lucide-react"
import { toast } from "sonner"
import axios from "axios"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"

// Phone number formatting utility
const formatPhoneNumber = (value: string) => {
  // Remove all non-digits
  const phoneNumber = value.replace(/\D/g, '')

  // Format as (XXX) XXX-XXXX
  if (phoneNumber.length >= 6) {
    return `(${phoneNumber.slice(0, 3)}) ${phoneNumber.slice(3, 6)}-${phoneNumber.slice(6, 10)}`
  } else if (phoneNumber.length >= 3) {
    return `(${phoneNumber.slice(0, 3)}) ${phoneNumber.slice(3)}`
  } else {
    return phoneNumber
  }
}

// Zip code formatting utility
const formatZipCode = (value: string) => {
  // Remove all non-digits and limit to 5 characters
  return value.replace(/\D/g, '').slice(0, 5)
}

// Date validation utility
const isValidAge = (dateString: string) => {
  const birthDate = new Date(dateString)
  const today = new Date()
  const age = today.getFullYear() - birthDate.getFullYear()
  const monthDiff = today.getMonth() - birthDate.getMonth()
  
  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
    return age - 1 >= 18
  }
  return age >= 18
}

// Check if user is a minor (under 18)
const isMinor = (dateString: string) => {
  if (!dateString) return false
  const birthDate = new Date(dateString)
  const today = new Date()
  const age = today.getFullYear() - birthDate.getFullYear()
  const monthDiff = today.getMonth() - birthDate.getMonth()
  
  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
    return age - 1 < 18
  }
  return age < 18
}

// Date input validation utility - simplified
const handleDateChange = (value: string, onChange: (value: string) => void) => {
  // Simply pass through the value and let the browser handle date input formatting
  onChange(value)
  
  // Only validate if it's a complete date to check for future dates
  if (value && value.match(/^\d{4}-\d{2}-\d{2}$/)) {
    const inputDate = new Date(value)
    const today = new Date()
    
    // If the date is in the future, reset to today's date
    if (inputDate > today) {
      onChange(today.toISOString().split('T')[0])
    }
  }
}

// Form validation schema
const formSchema = z.object({
  firstName: z.string().min(1, { message: "First name is required" }),
  lastName: z.string().min(1, { message: "Last name is required" }),
  email: z.string().email({ message: "Invalid email address" }),
  phone: z.string()
    .min(1, { message: "Phone number is required" })
    .refine((phone) => {
      const digits = phone.replace(/\D/g, '')
      return digits.length === 10
    }, { message: "Please enter a valid 10-digit phone number" }),
  dob: z.string()
    .min(1, { message: "Date of birth is required" })
    .refine((date) => {
      const birthDate = new Date(date)
      const today = new Date()
      return birthDate <= today
    }, { message: "Date of birth cannot be in the future" }),
  zipcode: z.string().min(5, { message: "Valid zipcode is required" }),
  gender: z.string().min(1, { message: "Gender is required" }),
  insurance: z.string().optional(),
  memberId: z.string().optional(),
  password: z.string().min(8, { message: "Password must be at least 8 characters" }),
})

type FormValues = z.infer<typeof formSchema>

export default function RegistrationAdultPage() {
  const router = useRouter()
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [showPassword, setShowPassword] = useState(false)
  const [insuranceOptions, setInsuranceOptions] = useState<{ _id: string; organizationName: string }[]>([])
  const [insuranceLoading, setInsuranceLoading] = useState(true)
  const [insuranceError, setInsuranceError] = useState<string | null>(null)
  const [formError, setFormError] = useState<string | null>(null)
  const [apiError, setApiError] = useState(false)
  const [showMinorDialog, setShowMinorDialog] = useState(false)

  // Google signup states
  const [googleUserData, setGoogleUserData] = useState<any>(null)
  const [showGoogleForm, setShowGoogleForm] = useState(false)

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      firstName: "",
      lastName: "",
      email: "",
      phone: "",
      dob: "",
      zipcode: "",
      gender: "",
      insurance: "",
      memberId: "",
      password: "",
    },
  })

  // Google form schema for additional info
  const googleFormSchema = z.object({
    phone: z.string()
      .min(1, { message: "Phone number is required" })
      .refine((phone) => {
        const digits = phone.replace(/\D/g, '')
        return digits.length === 10
      }, { message: "Please enter a valid 10-digit phone number" }),
    dob: z.string()
      .min(1, { message: "Date of birth is required" })
      .refine((date) => {
        const birthDate = new Date(date)
        const today = new Date()
        return birthDate <= today
      }, { message: "Date of birth cannot be in the future" }),
    zipcode: z.string().min(5, { message: "Valid zipcode is required" }),
    insurance: z.string().optional(),
    memberId: z.string().optional(),
  })

  type GoogleFormValues = z.infer<typeof googleFormSchema>

  const googleForm = useForm<GoogleFormValues>({
    resolver: zodResolver(googleFormSchema),
    defaultValues: {
      phone: "",
      dob: "",
      zipcode: "",
      insurance: "",
      memberId: "",
    },
  })

  // Get onboarding data from localStorage
  useEffect(() => {
    let onboardingData: any = null
    let selectedState = ""
    let selectedInsurance = ""
    try {
      const stored = localStorage.getItem("onboardingData")
      if (stored) {
        onboardingData = JSON.parse(stored)
        selectedState = onboardingData.state || ""
        selectedInsurance = onboardingData.insuranceCarrier || ""
      }
    } catch (e) {}

    // Fetch insurance options from backend, filtered by state if available
    const fetchInsurance = async () => {
      setInsuranceLoading(true)
      setInsuranceError(null)
      try {
        const res = await axios.get("/api/insurance-companies")
        let options = res.data.data || []
        if (selectedState) {
          options = options.filter((opt: any) => (opt.states || []).includes(selectedState) || (opt.states || []).includes("All"))
        }
        // Sort insurance options alphabetically
        options = options.sort((a: any, b: any) => a.organizationName.localeCompare(b.organizationName))
        setInsuranceOptions(options)
        // If user has a previous selection, set it as default
        if (selectedInsurance && options.some((opt: any) => opt.organizationName === selectedInsurance)) {
          form.setValue("insurance", selectedInsurance)
        }
      } catch (err) {
        setInsuranceError("Could not load insurance companies")
      } finally {
        setInsuranceLoading(false)
      }
    }
    fetchInsurance()
  }, [])

  // Handle date of birth change with minor detection
  const handleDobChange = (value: string, onChange: (value: string) => void) => {
    console.log('[DOB-CHANGE] Date changed:', value);
    // First apply the simplified date validation
    handleDateChange(value, onChange)

    // Check if the entered date indicates a minor
    if (value && value.match(/^\d{4}-\d{2}-\d{2}$/) && isMinor(value)) {
      setShowMinorDialog(true)
    }
  }

  // Handle registering as a minor
  const handleRegisterAsMinor = () => {
    // Get current form values
    const currentValues = form.getValues()
    
    // Store the current form data for prefilling minor registration
    const minorRegistrationData = {
      firstName: currentValues.firstName,
      lastName: currentValues.lastName,
      email: currentValues.email,
      phone: currentValues.phone,
      dob: currentValues.dob,
      zipcode: currentValues.zipcode,
      gender: currentValues.gender,
      insurance: currentValues.insurance,
      memberId: currentValues.memberId,
      password: currentValues.password
    }
    
    localStorage.setItem("minorRegistrationPrefill", JSON.stringify(minorRegistrationData))
    setShowMinorDialog(false)
    
    // Redirect to minor registration
    router.push("/registration-minor")
  }

  // Handle editing date of birth
  const handleEditDateOfBirth = () => {
    setShowMinorDialog(false)
    // Clear the date of birth field so user can enter a new one
    form.setValue("dob", "")
    // Focus on the date field
    const dobField = document.querySelector('input[name="dob"]') as HTMLInputElement
    if (dobField) {
      dobField.focus()
    }
  }

  const handleSubmit = async (values: FormValues) => {
    // Check if the user is a minor before submitting
    if (isMinor(values.dob)) {
      setShowMinorDialog(true)
      return
    }

    setIsSubmitting(true)
    setApiError(false)
    setFormError(null)
    try {
      // Format phone number
      const phoneDigitsOnly = values.phone.replace(/\D/g, "");
      const formattedPhone = phoneDigitsOnly.startsWith("1") 
        ? "+" + phoneDigitsOnly 
        : "+1" + phoneDigitsOnly;
      
      // Get therapist and appointment info from localStorage
      let therapistId = "";
      let appointmentStart = "";
      let appointmentEnd = "";
      
      const storedTherapist = localStorage.getItem("selectedTherapist");
      console.log("DEBUG - selectedTherapist from localStorage:", storedTherapist);
      
      if (storedTherapist) {
        try {
          const therapistData = JSON.parse(storedTherapist);
          console.log("DEBUG - Parsed therapist data:", therapistData);
          
          therapistId = therapistData._id || therapistData.id || "";
          
          if (!therapistId && therapistData.therapistId) {
            therapistId = therapistData.therapistId;
          }
          
          console.log("DEBUG - Found therapistId:", therapistId);
        } catch (error) {
          console.error("Error parsing therapist data:", error);
        }
      }
      
      const storedTherapistInfo = localStorage.getItem("therapist");
      if (!therapistId && storedTherapistInfo) {
        try {
          const therapistInfo = JSON.parse(storedTherapistInfo);
          therapistId = therapistInfo._id || therapistInfo.id || "";
          console.log("DEBUG - Found therapistId from therapist item:", therapistId);
        } catch (error) {
          console.error("Error parsing therapist info:", error);
        }
      }
      
      const storedAppointment = localStorage.getItem("appointmentDetails");
      console.log("DEBUG - appointmentDetails from localStorage:", storedAppointment);
      
      if (storedAppointment) {
        try {
          const appointmentData = JSON.parse(storedAppointment);
          console.log("DEBUG - Parsed appointment data:", appointmentData);
          
          if (appointmentData.date) {
            if (appointmentData.timeSlot && !appointmentData.timeSlot.includes(" - ")) {
              const date = new Date(appointmentData.date);
              const timeParts = appointmentData.timeSlot.split(":");
              
              if (timeParts.length >= 2) {
                let hours = parseInt(timeParts[0], 10);
                const minutes = parseInt(timeParts[1], 10);
                
                const isPM = /pm/i.test(appointmentData.timeSlot);
                if (isPM && hours < 12) hours += 12;
                
                const startDate = new Date(date);
                startDate.setHours(hours, minutes, 0, 0);
                
                const endDate = new Date(startDate);
                endDate.setHours(endDate.getHours() + 1);
                
                appointmentStart = startDate.toISOString();
                appointmentEnd = endDate.toISOString();
                
                console.log("DEBUG - Created appointment time from simple format:", {
                  appointmentStart,
                  appointmentEnd
                });
              }
            } 
            else if (appointmentData.timeSlot && appointmentData.timeSlot.includes(" - ")) {
              const timeSlotParts = appointmentData.timeSlot.split(" - ");
              if (timeSlotParts.length === 2) {
                const startTimeParts = timeSlotParts[0].split(":");
                const endTimeParts = timeSlotParts[1].split(":");
                
                if (startTimeParts.length >= 2 && endTimeParts.length >= 2) {
                  let startHour = parseInt(startTimeParts[0], 10);
                  let startMinute = 0;
                  
                  if (startTimeParts[1].match(/^\d+/)) {
                    startMinute = parseInt(startTimeParts[1].match(/^\d+/)[0], 10);
                  }
                  
                  const isStartPM = /pm/i.test(timeSlotParts[0]);
                  if (isStartPM && startHour < 12) startHour += 12;
                  
                  let endHour = parseInt(endTimeParts[0], 10);
                  let endMinute = 0;
                  
                  if (endTimeParts[1].match(/^\d+/)) {
                    endMinute = parseInt(endTimeParts[1].match(/^\d+/)[0], 10);
                  }
                  
                  const isEndPM = /pm/i.test(timeSlotParts[1]);
                  if (isEndPM && endHour < 12) endHour += 12;
                  
                  const startDate = new Date(appointmentData.date);
                  startDate.setHours(startHour, startMinute, 0, 0);
                  
                  const endDate = new Date(appointmentData.date);
                  endDate.setHours(endHour, endMinute, 0, 0);
                  
                  appointmentStart = startDate.toISOString();
                  appointmentEnd = endDate.toISOString();
                  
                  console.log("DEBUG - Created appointment time from range format:", {
                    appointmentStart,
                    appointmentEnd
                  });
                }
              }
            }
          }
        } catch (error) {
          console.error("Error parsing appointment data:", error);
        }
      }
      
      const payload = {
        userData: {
          firstname: values.firstName,
          lastname: values.lastName,
          email: values.email,
          username: values.email,
          dateOfBirth: values.dob,
          gender: values.gender,
          primaryPhone: formattedPhone,
          state: "",
          zipCode: values.zipcode,
          password: values.password,
          role: "CLIENT",
          skip: false,
          PersonalizeMatchData: {}
        },
        likeTherapist: {
          therapistId: therapistId
        },
        appointmentObj: {
          start: appointmentStart,
          end: appointmentEnd,
          title: "Initial Session",
          reminders: [],
          color: "#3788d8",
          groupId: "",
          therapistId: therapistId,
          repeatInfo: {
            repeatType: "DOES NOT REPEAT",
            interval: "1",
            repeatDays: {
              sunday: false,
              monday: false,
              tuesday: false,
              wednesday: false,
              thursday: false,
              friday: false,
              saturday: false
            },
            endingDate: "",
            endingAfter: null,
            endingType: ""
          }
        },
        referralInfo: {
          referralCode: ""
        }
      }

      localStorage.setItem("userData", JSON.stringify(values))
      localStorage.setItem("password", JSON.stringify(values.password))

      const response = await fetch("https://api.lavnihealth.com/api/public/registerWithEmail", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(payload),
      })

      const data = await response.json()

      if (data && data.data && data.data._id) {
        localStorage.setItem("registeredUser", JSON.stringify(data.data));
      }

      if (response.ok && (!data.success || data.success === undefined)) {
        if (data.error || data.message) {
          setFormError(data.error || data.message || "Registration failed. Please try again.")
          setApiError(true)
          toast.error(data.error || data.message || "Registration failed. Please try again.", {
            duration: 6000,
            position: "top-right"
          })
          console.error("API Registration Error:", data)
          window.scrollTo({ top: 0, behavior: 'smooth' });
          setIsSubmitting(false)
          return
        }
      }
      if (response.ok && (data.success === true || data.status === 'success')) {
        toast.success("Registration successful!", {
          duration: 6000,
          position: "top-right"
        });
        router.push("/verification");
      } else {
        setFormError(data.error || data.message || "Registration failed. Please try again.")
        setApiError(true)
        toast.error("An error occurred while connecting to the server. Please try again later.", {
          duration: 6000,
          position: "top-right"
        });
        console.error("API Registration Error:", data)
        window.scrollTo({ top: 0, behavior: 'smooth' });
        setIsSubmitting(false);
      }
    } catch (error) {
      setFormError("An error occurred while connecting to the server. Please try again later.")
      setApiError(true)
      console.error("Registration error:", error)
      toast.error("An error occurred while connecting to the server. Please try again later.", {
        duration: 6000,
        position: "top-right"
      });
      window.scrollTo({ top: 0, behavior: 'smooth' });
      setIsSubmitting(false)
    }
  }

  // Auto-scroll to first error field on validation error
  const onError = (errors: any) => {
    const firstErrorField = Object.keys(errors)[0];
    const el = document.querySelector(`[name='${firstErrorField}']`);
    if (el && el.scrollIntoView) {
      el.scrollIntoView({ behavior: 'smooth', block: 'center' });
      (el as HTMLElement).focus();
    }
  };

  const handleGoogleSignIn = async (googleUserData: any) => {
    console.log('[GOOGLE-SIGNUP] Received Google user data:', googleUserData);

    // Store Google user data and show the additional info form
    setGoogleUserData(googleUserData);
    setShowGoogleForm(true);

    // Pre-fill insurance if available from onboarding
    const onboardingData = JSON.parse(localStorage.getItem("onboardingData") || "{}");
    if (onboardingData.insuranceCarrier && insuranceOptions.some(opt => opt.organizationName === onboardingData.insuranceCarrier)) {
      googleForm.setValue("insurance", onboardingData.insuranceCarrier);
    }
  };

  const handleGoogleFormSubmit = async (values: GoogleFormValues) => {
    console.log('[GOOGLE-FORM-SUBMIT] Form submitted with values:', values);
    console.log('[GOOGLE-FORM-SUBMIT] Form errors:', googleForm.formState.errors);
    console.log('[GOOGLE-FORM-SUBMIT] Form is valid:', googleForm.formState.isValid);

    if (!googleUserData) {
      toast.error("Google authentication data not found. Please try again.");
      return;
    }

    // Check if the user is a minor before submitting
    if (isMinor(values.dob)) {
      setShowMinorDialog(true);
      return;
    }

    setIsSubmitting(true);
    try {
      console.log('[GOOGLE-SIGNUP] Starting Google registration with additional info:', values);

      const onboardingData = JSON.parse(localStorage.getItem("onboardingData") || "{}");

      // Format phone number
      const phoneDigitsOnly = values.phone.replace(/\D/g, "");
      const formattedPhone = phoneDigitsOnly.startsWith("1")
        ? "+" + phoneDigitsOnly
        : "+1" + phoneDigitsOnly;

      // Get therapist and appointment info from localStorage (same as email registration)
      let therapistId = "";
      let appointmentStart = "";
      let appointmentEnd = "";
      
      const storedTherapist = localStorage.getItem("selectedTherapist");
      console.log("[GOOGLE-SIGNUP] selectedTherapist from localStorage:", storedTherapist);
      
      if (storedTherapist) {
        try {
          const therapistData = JSON.parse(storedTherapist);
          console.log("[GOOGLE-SIGNUP] Parsed therapist data:", therapistData);
          
          therapistId = therapistData._id || therapistData.id || "";
          
          if (!therapistId && therapistData.therapistId) {
            therapistId = therapistData.therapistId;
          }
          
          console.log("[GOOGLE-SIGNUP] Found therapistId:", therapistId);
        } catch (error) {
          console.error("[GOOGLE-SIGNUP] Error parsing therapist data:", error);
        }
      }
      
      const storedTherapistInfo = localStorage.getItem("therapist");
      if (!therapistId && storedTherapistInfo) {
        try {
          const therapistInfo = JSON.parse(storedTherapistInfo);
          therapistId = therapistInfo._id || therapistInfo.id || "";
          console.log("[GOOGLE-SIGNUP] Found therapistId from therapist item:", therapistId);
        } catch (error) {
          console.error("[GOOGLE-SIGNUP] Error parsing therapist info:", error);
        }
      }
      
      const storedAppointment = localStorage.getItem("appointmentDetails");
      console.log("[GOOGLE-SIGNUP] appointmentDetails from localStorage:", storedAppointment);
      
      if (storedAppointment) {
        try {
          const appointmentData = JSON.parse(storedAppointment);
          console.log("[GOOGLE-SIGNUP] Parsed appointment data:", appointmentData);
          
          if (appointmentData.date) {
            if (appointmentData.timeSlot && !appointmentData.timeSlot.includes(" - ")) {
              const date = new Date(appointmentData.date);
              const timeParts = appointmentData.timeSlot.split(":");
              
              if (timeParts.length >= 2) {
                let hours = parseInt(timeParts[0], 10);
                const minutes = parseInt(timeParts[1], 10);
                
                const isPM = /pm/i.test(appointmentData.timeSlot);
                if (isPM && hours < 12) hours += 12;
                
                const startDate = new Date(date);
                startDate.setHours(hours, minutes, 0, 0);
                
                const endDate = new Date(startDate);
                endDate.setHours(endDate.getHours() + 1);
                
                appointmentStart = startDate.toISOString();
                appointmentEnd = endDate.toISOString();
                
                console.log("[GOOGLE-SIGNUP] Created appointment time from simple format:", {
                  appointmentStart,
                  appointmentEnd
                });
              }
            } 
            else if (appointmentData.timeSlot && appointmentData.timeSlot.includes(" - ")) {
              const timeSlotParts = appointmentData.timeSlot.split(" - ");
              if (timeSlotParts.length === 2) {
                const startTimeParts = timeSlotParts[0].split(":");
                const endTimeParts = timeSlotParts[1].split(":");
                
                if (startTimeParts.length >= 2 && endTimeParts.length >= 2) {
                  let startHour = parseInt(startTimeParts[0], 10);
                  let startMinute = 0;
                  
                  if (startTimeParts[1].match(/^\d+/)) {
                    startMinute = parseInt(startTimeParts[1].match(/^\d+/)[0], 10);
                  }
                  
                  const isStartPM = /pm/i.test(timeSlotParts[0]);
                  if (isStartPM && startHour < 12) startHour += 12;
                  
                  let endHour = parseInt(endTimeParts[0], 10);
                  let endMinute = 0;
                  
                  if (endTimeParts[1].match(/^\d+/)) {
                    endMinute = parseInt(endTimeParts[1].match(/^\d+/)[0], 10);
                  }
                  
                  const isEndPM = /pm/i.test(timeSlotParts[1]);
                  if (isEndPM && endHour < 12) endHour += 12;
                  
                  const startDate = new Date(appointmentData.date);
                  startDate.setHours(startHour, startMinute, 0, 0);
                  
                  const endDate = new Date(appointmentData.date);
                  endDate.setHours(endHour, endMinute, 0, 0);
                  
                  appointmentStart = startDate.toISOString();
                  appointmentEnd = endDate.toISOString();
                  
                  console.log("[GOOGLE-SIGNUP] Created appointment time from range format:", {
                    appointmentStart,
                    appointmentEnd
                  });
                }
              }
            }
          }
        } catch (error) {
          console.error("[GOOGLE-SIGNUP] Error parsing appointment data:", error);
        }
      }

      const lavniApiUrl = process.env.NEXT_PUBLIC_LAVNI_API_URL || 'https://api.lavnihealth.com';
      const registrationPayload = {
        userData: {
          idToken: googleUserData.idToken,
          clientId: googleUserData.clientId,
          role: "CLIENT",
          primaryPhone: formattedPhone,
          firstname: googleUserData.firstname,
          lastname: googleUserData.lastname,
          gender: googleUserData.gender || "prefer-not-to-say",
          dateOfBirth: values.dob,
          ethnicityId: "65b9bfb7ad5d1dc915d76585", // Default ethnicity ID
          username: googleUserData.email.split('@')[0],
          state: onboardingData.state || "Unknown",
          zipCode: values.zipcode,
          skip: false // Changed to false since we have real data
        },
        likeTherapist: {
          therapistId: therapistId
        },
        appointmentObj: {
          start: appointmentStart,
          end: appointmentEnd,
          title: "Initial Session",
          reminders: [],
          color: "#3788d8",
          groupId: "",
          therapistId: therapistId,
          repeatInfo: {
            repeatType: "DOES NOT REPEAT",
            interval: "1",
            repeatDays: {
              sunday: false,
              monday: false,
              tuesday: false,
              wednesday: false,
              thursday: false,
              friday: false,
              saturday: false
            },
            endingDate: "",
            endingAfter: null,
            endingType: ""
          }
        },
        referralInfo: {
          referralCode: ""
        }
      };

      console.log('[GOOGLE-SIGNUP] Calling Lavni API with payload:', registrationPayload);

      const lavniResponse = await fetch(`${lavniApiUrl}/api/public/signUpWithGoogleReact`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(registrationPayload),
      });

      const lavniResult = await lavniResponse.json();
      console.log('[GOOGLE-SIGNUP] Lavni API response:', lavniResult);

      if (!lavniResponse.ok || !lavniResult.success) {
        throw new Error(lavniResult.message || 'Failed to register with Lavni API');
      }

      if (lavniResult.data && lavniResult.data._id) {
        console.log('[GOOGLE-SIGNUP] Updating user profile for adult client');

        const profileUpdatePayload = {
          userId: lavniResult.data._id,
          clientType: 'adult',
          additionalData: {
            medium: 'GOOGLE',
            googleId: googleUserData.email,
          }
        };

        const profileResponse = await fetch('/api/update-user-profile', {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(profileUpdatePayload),
        });

        const profileResult = await profileResponse.json();
        console.log('[GOOGLE-SIGNUP] Profile update response:', profileResult);

        if (!profileResponse.ok) {
          console.warn('[GOOGLE-SIGNUP] Profile update failed, but continuing:', profileResult);
        }
      }

      localStorage.setItem("registeredUser", JSON.stringify(lavniResult.data));
      localStorage.setItem("userData", JSON.stringify({
        firstName: googleUserData.firstname,
        lastName: googleUserData.lastname,
        email: googleUserData.email,
        phone: formattedPhone,
        dob: values.dob,
        zipcode: values.zipcode,
        gender: googleUserData.gender || "prefer-not-to-say",
        insurance: values.insurance,
        memberId: values.memberId,
        registrationType: 'google'
      }));

      console.log('[GOOGLE-SIGNUP] Google registration successful, redirecting to verification');
      toast.success("Registration successful! Please verify your account.");

      router.push("/verification");

    } catch (error) {
      console.error('[GOOGLE-SIGNUP] Error:', error);
      toast.error(error instanceof Error ? error.message : "Failed to register with Google");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleGoogleError = (error: any) => {
    console.error('[GOOGLE-SIGNUP] Google OAuth error:', error);
    toast.error("Failed to sign in with Google. Please try again.");
  };

  return (
    <main className="flex min-h-screen flex-col bg-white">
      <div className="flex-1 px-4 py-6 pb-36">
        <div className="mb-6">
          <h1 className="text-2xl font-bold text-gray-900">Create Your Account</h1>
          <p className="text-gray-600 mt-2">Please provide your information to complete registration.</p>
        </div>

        <MobileStepTracker currentStep={5} totalSteps={7} />

        {!showGoogleForm && (
          <>
            <GoogleOAuthButton
              onSuccess={handleGoogleSignIn}
              onError={handleGoogleError}
              clientType="adult"
              disabled={isSubmitting}
            />

            <div className="relative my-6">
              <div className="absolute inset-0 flex items-center">
                <div className="w-full border-t border-gray-300"></div>
              </div>
              <div className="relative flex justify-center text-sm">
                <span className="px-2 bg-white text-gray-500">or continue with email</span>
              </div>
            </div>
          </>
        )}

        {showGoogleForm && googleUserData && (
          <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <div className="flex items-center gap-3 mb-3">
              <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                <svg className="w-4 h-4 text-blue-600" viewBox="0 0 24 24">
                  <path fill="currentColor" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                  <path fill="currentColor" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                  <path fill="currentColor" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                  <path fill="currentColor" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                </svg>
              </div>
              <div>
                <p className="text-sm font-medium text-blue-900">Signed in with Google</p>
                <p className="text-xs text-blue-700">{googleUserData.email}</p>
              </div>
            </div>
            <p className="text-sm text-blue-800">Please complete your registration by providing additional information below.</p>
          </div>
        )}

        {!showGoogleForm ? (
          <Form {...form}>
            <form id="registration-form" onSubmit={form.handleSubmit(handleSubmit, onError)} className="space-y-6">
            {formError && (
              <div className="text-red-500 text-sm mb-4 border border-red-500 bg-red-50 rounded p-2">{formError}</div>
            )}
            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="firstName"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-base">First Name</FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        placeholder="Enter first name"
                        className={`h-14 text-base ${(apiError || form.formState.errors.firstName) ? 'border-red-500' : ''}`}
                        disabled={isSubmitting}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="lastName"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-base">Last Name</FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        placeholder="Enter last name"
                        className={`h-14 text-base ${(apiError || form.formState.errors.lastName) ? 'border-red-500' : ''}`}
                        disabled={isSubmitting}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-base">Email</FormLabel>
                  <FormControl>
                    <Input
                      {...field}
                      type="email"
                      placeholder="Enter your email"
                      className={`h-14 text-base ${(apiError || form.formState.errors.email) ? 'border-red-500' : ''}`}
                      disabled={isSubmitting}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="phone"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-base">Phone Number</FormLabel>
                  <FormControl>
                    <Input
                      {...field}
                      type="tel"
                      placeholder="(*************"
                      className={`h-14 text-base ${(apiError || form.formState.errors.phone) ? 'border-red-500' : ''}`}
                      disabled={isSubmitting}
                      onChange={(e) => {
                        const formatted = formatPhoneNumber(e.target.value)
                        field.onChange(formatted)
                      }}
                      onKeyPress={(e) => {
                        const allowedKeys = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'Backspace', 'Delete', 'Tab', 'ArrowLeft', 'ArrowRight']
                        if (!allowedKeys.includes(e.key) && !e.ctrlKey && !e.metaKey) {
                          e.preventDefault()
                        }
                      }}
                      maxLength={14}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="dob"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-base">Date of Birth</FormLabel>
                  <FormControl>
                    <Input 
                      {...field} 
                      type="date" 
                      className={`h-14 text-base ${(apiError || form.formState.errors.dob) ? 'border-red-500' : ''}`} 
                      disabled={isSubmitting}
                      max={new Date().toISOString().split('T')[0]}
                      onChange={(e) => handleDobChange(e.target.value, field.onChange)}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="zipcode"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-base">Zip Code</FormLabel>
                  <FormControl>
                    <Input
                      {...field}
                      placeholder="Enter your zip code"
                      className={`h-14 text-base ${(apiError || form.formState.errors.zipcode) ? 'border-red-500' : ''}`}
                      disabled={isSubmitting}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="gender"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-base">Gender</FormLabel>
                  <FormControl>
                    <Select onValueChange={field.onChange} defaultValue={field.value} disabled={isSubmitting || insuranceLoading}>
                      <SelectTrigger className={`h-14 text-base ${(apiError || form.formState.errors.gender) ? 'border-red-500' : ''}`}>
                        <SelectValue placeholder="Select your gender" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="male">Male</SelectItem>
                        <SelectItem value="female">Female</SelectItem>
                        <SelectItem value="non-binary">Non-binary</SelectItem>
                        <SelectItem value="other">Other</SelectItem>
                        <SelectItem value="prefer-not-to-say">Prefer not to say</SelectItem>
                      </SelectContent>
                    </Select>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="insurance"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-base">Insurance (Optional)</FormLabel>
                  <FormControl>
                    <Select onValueChange={field.onChange} value={field.value} disabled={isSubmitting || insuranceLoading}>
                      <SelectTrigger className={`h-14 text-base ${(apiError || form.formState.errors.insurance) ? 'border-red-500' : ''}`}>
                        <SelectValue placeholder={insuranceLoading ? "Loading..." : "Select your insurance"} />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem key="loading" value="loading">Loading...</SelectItem>
                        {insuranceError && <SelectItem key="error" value="error">{insuranceError}</SelectItem>}
                        {!insuranceLoading && !insuranceError && insuranceOptions.length === 0 && (
                          <SelectItem key="no-data" value="no-data">No insurance companies found</SelectItem>
                        )}
                        {!insuranceLoading && insuranceOptions.map((opt, index) => (
                          <SelectItem key={opt._id || `insurance-${index}`} value={opt.organizationName}>{opt.organizationName}</SelectItem>
                        ))}
                        <SelectItem key="other" value="other">Other</SelectItem>
                      </SelectContent>
                    </Select>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="memberId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-base">Member ID (Optional)</FormLabel>
                  <FormControl>
                    <Input
                      {...field}
                      placeholder="Enter your member ID"
                      className={`h-14 text-base ${(apiError || form.formState.errors.memberId) ? 'border-red-500' : ''}`}
                      disabled={isSubmitting}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="password"
              render={({ field }) => (
                <FormItem className="mb-8">
                  <FormLabel className="text-base">Password</FormLabel>
                  <FormControl>
                    <div className="relative">
                      <Input
                        {...field}
                        type={showPassword ? "text" : "password"}
                        placeholder="Create a password"
                        className={`h-14 text-base pr-12 ${(apiError || form.formState.errors.password) ? 'border-red-500' : ''}`}
                        disabled={isSubmitting}
                      />
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        className="absolute right-0 top-0 h-14 px-3 py-2 hover:bg-transparent"
                        onClick={() => setShowPassword(!showPassword)}
                        disabled={isSubmitting}
                      >
                        {showPassword ? (
                          <EyeOff className="h-4 w-4 text-gray-500" />
                        ) : (
                          <Eye className="h-4 w-4 text-gray-500" />
                        )}
                        <span className="sr-only">
                          {showPassword ? "Hide password" : "Show password"}
                        </span>
                      </Button>
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </form>
        </Form>
        ) : (
          <Form {...googleForm}>
            <form id="google-registration-form" onSubmit={(e) => {
              console.log('[FORM-SUBMIT] Form onSubmit triggered');
              console.log('[FORM-SUBMIT] Form state:', googleForm.formState);
              console.log('[FORM-SUBMIT] Form values:', googleForm.getValues());
              return googleForm.handleSubmit(handleGoogleFormSubmit)(e);
            }} className="space-y-6">
              {formError && (
                <div className="text-red-500 text-sm mb-4 border border-red-500 bg-red-50 rounded p-2">{formError}</div>
              )}

              {/* Display Google user info */}
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-base font-medium text-gray-700">First Name</label>
                  <div className="mt-1 p-3 bg-gray-50 border border-gray-200 rounded-md text-base text-gray-900">
                    {googleUserData.firstname}
                  </div>
                </div>
                <div>
                  <label className="text-base font-medium text-gray-700">Last Name</label>
                  <div className="mt-1 p-3 bg-gray-50 border border-gray-200 rounded-md text-base text-gray-900">
                    {googleUserData.lastname}
                  </div>
                </div>
              </div>

              <div>
                <label className="text-base font-medium text-gray-700">Email</label>
                <div className="mt-1 p-3 bg-gray-50 border border-gray-200 rounded-md text-base text-gray-900">
                  {googleUserData.email}
                </div>
              </div>

              <FormField
                control={googleForm.control}
                name="phone"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-base">Phone Number</FormLabel>
                    <FormControl>
                      <Input
                        name={field.name}
                        ref={field.ref}
                        value={field.value || ""}
                        type="tel"
                        placeholder="(*************"
                        className={`h-14 text-base ${googleForm.formState.errors.phone ? 'border-red-500' : ''}`}
                        disabled={isSubmitting}
                        onChange={(e) => {
                          console.log('[GOOGLE-PHONE-CHANGE] Phone changed:', e.target.value);
                          const formatted = formatPhoneNumber(e.target.value)
                          console.log('[GOOGLE-PHONE-CHANGE] Formatted phone:', formatted);

                          // Use setValue to force update
                          googleForm.setValue('phone', formatted, { shouldValidate: true, shouldDirty: true })

                          console.log('[GOOGLE-PHONE-CHANGE] Form phone value after setValue:', googleForm.getValues('phone'));
                        }}
                        onBlur={field.onBlur}
                        maxLength={14}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={googleForm.control}
                name="dob"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-base">Date of Birth</FormLabel>
                    <FormControl>
                      <Input
                        name={field.name}
                        ref={field.ref}
                        value={field.value || ""}
                        type="date"
                        className={`h-14 text-base ${googleForm.formState.errors.dob ? 'border-red-500' : ''}`}
                        disabled={isSubmitting}
                        max={new Date().toISOString().split('T')[0]}
                        onChange={(e) => {
                          console.log('[GOOGLE-DOB-CHANGE] Date changed:', e.target.value);

                          // Use setValue to force update
                          googleForm.setValue('dob', e.target.value, { shouldValidate: true, shouldDirty: true })

                          // Check if the entered date indicates a minor
                          if (e.target.value && e.target.value.match(/^\d{4}-\d{2}-\d{2}$/) && isMinor(e.target.value)) {
                            setShowMinorDialog(true)
                          }
                        }}
                        onBlur={field.onBlur}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={googleForm.control}
                name="zipcode"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-base">Zip Code</FormLabel>
                    <FormControl>
                      <Input
                        name={field.name}
                        ref={field.ref}
                        value={field.value || ""}
                        placeholder="Enter your zip code"
                        className={`h-14 text-base ${googleForm.formState.errors.zipcode ? 'border-red-500' : ''}`}
                        disabled={isSubmitting}
                        onChange={(e) => {
                          console.log('[GOOGLE-ZIPCODE-CHANGE] Zipcode changed:', e.target.value);
                          const formatted = formatZipCode(e.target.value)
                          console.log('[GOOGLE-ZIPCODE-CHANGE] Formatted zipcode:', formatted);

                          // Use setValue to force update
                          googleForm.setValue('zipcode', formatted, { shouldValidate: true, shouldDirty: true })

                          console.log('[GOOGLE-ZIPCODE-CHANGE] Form zipcode value after setValue:', googleForm.getValues('zipcode'));
                        }}
                        onBlur={field.onBlur}
                        maxLength={5}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

            </form>
          </Form>
        )}
      </div>

      <StickyBottomCTA
        primaryText={showGoogleForm ? "Create Account with Google" : "Create Account"}
        primaryAction={() => {
          console.log('[BUTTON-CLICK] Primary action clicked, showGoogleForm:', showGoogleForm);
          if (showGoogleForm) {
            console.log('[BUTTON-CLICK] Dispatching submit event to google-registration-form');
            const form = document.getElementById("google-registration-form");
            console.log('[BUTTON-CLICK] Found form element:', form);
            form?.dispatchEvent(new Event("submit", { cancelable: true, bubbles: true }))
          } else {
            console.log('[BUTTON-CLICK] Dispatching submit event to registration-form');
            document
              .getElementById("registration-form")
              ?.dispatchEvent(new Event("submit", { cancelable: true, bubbles: true }))
          }
        }}
        secondaryText="Back"
        secondaryAction={() => {
          // Always go back to previous page (registration type selection)
          router.back()
        }}
        isDisabled={isSubmitting}
      />

      {showMinorDialog && (
        <Dialog open={showMinorDialog} onOpenChange={setShowMinorDialog}>
          <DialogContent className="sm:max-w-[425px] bg-white">
            <DialogHeader>
              <DialogTitle>Minor Registration Required</DialogTitle>
              <DialogDescription>
                Your date of birth indicates you are a minor. Please select from the following options:
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <Button 
                onClick={handleRegisterAsMinor}
                className="w-full"
                variant="default"
              >
                Register as a Minor
              </Button>
              <Button 
                onClick={handleEditDateOfBirth}
                className="w-full"
                variant="outline"
              >
                Edit My Date of Birth
              </Button>
            </div>
          </DialogContent>
        </Dialog>
      )}
    </main>
  )
}