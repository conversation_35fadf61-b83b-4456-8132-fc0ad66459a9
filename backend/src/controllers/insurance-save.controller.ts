import { Request, Response, NextFunction } from 'express';
import { InsuranceService, InsuranceData, EligibilityResult } from '../services/insurance.service';

export const savePrimaryInsurance = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const { userId, insuranceData, eligibilityResult } = req.body;

    console.log('[INSURANCE-CONTROLLER] Saving primary insurance for user:', userId);

    // Validate required fields
    if (!userId || !insuranceData) {
      return res.status(400).json({
        status: 'error',
        message: 'Missing required fields: userId and insuranceData'
      });
    }

    // Validate insurance data structure
    const requiredFields = ['carrier', 'memberId', 'firstName', 'lastName', 'dob'];
    for (const field of requiredFields) {
      if (!insuranceData[field]) {
        return res.status(400).json({
          status: 'error',
          message: `Missing required insurance field: ${field}`
        });
      }
    }

    // Save primary insurance using service
    const result = await InsuranceService.savePrimaryInsurance(
      userId,
      insuranceData as InsuranceData,
      eligibilityResult as EligibilityResult
    );

    res.status(201).json({
      status: 'success',
      message: 'Primary insurance saved successfully',
      data: result
    });

  } catch (error) {
    console.error('[INSURANCE-CONTROLLER] Error saving primary insurance:', error);
    next(error);
  }
};

export const saveSecondaryInsurance = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const { userId, insuranceData, eligibilityResult } = req.body;

    console.log('[INSURANCE-CONTROLLER] Saving secondary insurance for user:', userId);

    // Validate required fields
    if (!userId || !insuranceData) {
      return res.status(400).json({
        status: 'error',
        message: 'Missing required fields: userId and insuranceData'
      });
    }

    // Validate insurance data structure
    const requiredFields = ['carrier', 'memberId', 'firstName', 'lastName', 'dob'];
    for (const field of requiredFields) {
      if (!insuranceData[field]) {
        return res.status(400).json({
          status: 'error',
          message: `Missing required insurance field: ${field}`
        });
      }
    }

    // Save secondary insurance using service
    const result = await InsuranceService.saveSecondaryInsurance(
      userId,
      insuranceData as InsuranceData,
      eligibilityResult as EligibilityResult
    );

    res.status(201).json({
      status: 'success',
      message: 'Secondary insurance saved successfully',
      data: result
    });

  } catch (error) {
    console.error('[INSURANCE-CONTROLLER] Error saving secondary insurance:', error);
    next(error);
  }
};

export const getUserInsurance = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const { userId } = req.params;

    console.log('[INSURANCE-CONTROLLER] Getting insurance for user:', userId);

    if (!userId) {
      return res.status(400).json({
        status: 'error',
        message: 'Missing required parameter: userId'
      });
    }

    // Get user insurance using service
    const insurance = await InsuranceService.getUserInsurance(userId);

    res.status(200).json({
      status: 'success',
      data: insurance
    });

  } catch (error) {
    console.error('[INSURANCE-CONTROLLER] Error getting user insurance:', error);
    next(error);
  }
};

export const updateEligibilityDetails = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const { userId, eligibilityResult, isSecondary = false } = req.body;

    console.log('[INSURANCE-CONTROLLER] Updating eligibility details for user:', userId);

    if (!userId || !eligibilityResult) {
      return res.status(400).json({
        status: 'error',
        message: 'Missing required fields: userId and eligibilityResult'
      });
    }

    // Update eligibility details using service
    const success = await InsuranceService.updateEligibilityDetails(
      userId,
      eligibilityResult as EligibilityResult,
      isSecondary
    );

    res.status(200).json({
      status: 'success',
      message: 'Eligibility details updated successfully',
      data: { success }
    });

  } catch (error) {
    console.error('[INSURANCE-CONTROLLER] Error updating eligibility details:', error);
    next(error);
  }
};

export const removeInsurance = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const { userId } = req.params;
    const { isPrimary = true } = req.body;

    console.log('[INSURANCE-CONTROLLER] Removing insurance for user:', userId);

    if (!userId) {
      return res.status(400).json({
        status: 'error',
        message: 'Missing required parameter: userId'
      });
    }

    // Remove insurance using service
    const success = await InsuranceService.removeInsurance(userId, isPrimary);

    res.status(200).json({
      status: 'success',
      message: `${isPrimary ? 'Primary' : 'Secondary'} insurance removed successfully`,
      data: { success }
    });

  } catch (error) {
    console.error('[INSURANCE-CONTROLLER] Error removing insurance:', error);
    next(error);
  }
}; 