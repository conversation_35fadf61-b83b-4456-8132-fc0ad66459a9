import mongoose, { Document, Schema, Types } from 'mongoose';

export interface IUpload extends Document {
  isUrl: boolean;
  signRequired: boolean;
  userId: Types.ObjectId;
  originalName: string;
  name: string;
  type: string;
  path: string;
  extension: string;
  category: string;
  createdAt: Date;
  updatedAt: Date;
}

const uploadSchema = new Schema<IUpload>(
  {
    isUrl: Boolean,
    signRequired: Boolean,
    userId: { type: Schema.Types.ObjectId, ref: 'User' },
    originalName: String,
    name: String,
    type: String,
    path: String,
    extension: String,
    category: String,
  },
  { timestamps: true }
);

export const Upload = mongoose.model<IUpload>('Upload', uploadSchema); 