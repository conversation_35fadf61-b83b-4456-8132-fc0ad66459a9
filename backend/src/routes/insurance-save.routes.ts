import { Router } from 'express';
import {
  savePrimaryInsurance,
  saveSecondaryInsurance,
  getUserInsurance,
  updateEligibilityDetails,
  removeInsurance
} from '../controllers/insurance-save.controller';

const router = Router();

// Save primary insurance
router.post('/primary', savePrimaryInsurance);

// Save secondary insurance
router.post('/secondary', saveSecondaryInsurance);

// Get user insurance records
router.get('/user/:userId', getUserInsurance);

// Update eligibility details
router.put('/eligibility', updateEligibilityDetails);

// Remove insurance
router.delete('/user/:userId', removeInsurance);

export default router; 