import { Insurance, IInsurance } from '../models/InsuranceCompany';
import { User, IUser } from '../models/User';
import { InsuranceCompany } from '../models/InsuranceCompany';
import { Types } from 'mongoose';

export interface InsuranceData {
  carrier: string;
  memberId: string;
  firstName: string;
  lastName: string;
  dob: string;
  state?: string;
  address?: {
    address1?: string;
    city?: string;
    state?: string;
    postalCode?: string;
  };
}

export interface EligibilityResult {
  eligibilityStatus: string;
  copayAmount?: number;
  copayMessage?: string;
  result?: any;
}

export class InsuranceService {
  /**
   * Save primary insurance for a user
   */
  static async savePrimaryInsurance(
    userId: string,
    insuranceData: InsuranceData,
    eligibilityResult?: EligibilityResult
  ): Promise<{ insuranceId: string; success: boolean }> {
    try {
      console.log('[INSURANCE-SERVICE] Saving primary insurance for user:', userId);
      
      // Find the insurance company by name
      const insuranceCompany = await InsuranceCompany.findOne({
        organizationName: insuranceData.carrier
      });

      if (!insuranceCompany) {
        throw new Error(`Insurance company not found: ${insuranceData.carrier}`);
      }

      // Create insurance record
      const insuranceRecord = await Insurance.create({
        clientId: new Types.ObjectId(userId),
        insuranceCompanyId: insuranceCompany._id,
        subscriber: {
          memberId: insuranceData.memberId,
          firstName: insuranceData.firstName,
          lastName: insuranceData.lastName,
          address: insuranceData.address || {}
        }
      });

      // Update user with insurance ID and eligibility details
      const updateData: any = {
        insuranceId: insuranceRecord._id
      };

      // Add eligibility details if provided
      if (eligibilityResult && eligibilityResult.eligibilityStatus === 'ACTIVE') {
        updateData.claimEligibilityDetails = {
          subscriber: {
            memberId: insuranceData.memberId,
            firstName: insuranceData.firstName,
            lastName: insuranceData.lastName,
            entityIdentifier: "Insured or Subscriber",
            entityType: "Person",
            dateOfBirth: insuranceData.dob.replace(/-/g, ""),
            address: insuranceData.address || {}
          },
          memberId: insuranceData.memberId,
          eligibilityResult: eligibilityResult.result
        };
      }

      await User.findByIdAndUpdate(userId, updateData);

      console.log('[INSURANCE-SERVICE] Primary insurance saved successfully:', insuranceRecord._id);
      
      return {
        insuranceId: (insuranceRecord._id as Types.ObjectId).toString(),
        success: true
      };
    } catch (error) {
      console.error('[INSURANCE-SERVICE] Error saving primary insurance:', error);
      throw error;
    }
  }

  /**
   * Save secondary insurance for a user
   */
  static async saveSecondaryInsurance(
    userId: string,
    insuranceData: InsuranceData,
    eligibilityResult?: EligibilityResult
  ): Promise<{ insuranceId: string; success: boolean }> {
    try {
      console.log('[INSURANCE-SERVICE] Saving secondary insurance for user:', userId);
      
      // Find the insurance company by name
      const insuranceCompany = await InsuranceCompany.findOne({
        organizationName: insuranceData.carrier
      });

      if (!insuranceCompany) {
        throw new Error(`Insurance company not found: ${insuranceData.carrier}`);
      }

      // Create secondary insurance record
      const insuranceRecord = await Insurance.create({
        clientId: new Types.ObjectId(userId),
        insuranceCompanyId: insuranceCompany._id,
        subscriber: {
          memberId: insuranceData.memberId,
          firstName: insuranceData.firstName,
          lastName: insuranceData.lastName,
          address: insuranceData.address || {}
        }
      });

      // Update user with secondary insurance ID
      await User.findByIdAndUpdate(userId, {
        secondaryInsuranceId: insuranceRecord._id
      });

      console.log('[INSURANCE-SERVICE] Secondary insurance saved successfully:', insuranceRecord._id);
      
      return {
        insuranceId: (insuranceRecord._id as Types.ObjectId).toString(),
        success: true
      };
    } catch (error) {
      console.error('[INSURANCE-SERVICE] Error saving secondary insurance:', error);
      throw error;
    }
  }

  /**
   * Get user's insurance records
   */
  static async getUserInsurance(userId: string): Promise<{
    primary?: IInsurance;
    secondary?: IInsurance;
  }> {
    try {
      const user = await User.findById(userId)
        .populate('insuranceId')
        .populate('secondaryInsuranceId') as IUser | null;

      if (!user) {
        return {};
      }

      return {
        primary: user.insuranceId ? (user.insuranceId as unknown as IInsurance) : undefined,
        secondary: user.secondaryInsuranceId ? (user.secondaryInsuranceId as unknown as IInsurance) : undefined
      };
    } catch (error) {
      console.error('[INSURANCE-SERVICE] Error getting user insurance:', error);
      throw error;
    }
  }

  /**
   * Update eligibility details for a user
   */
  static async updateEligibilityDetails(
    userId: string,
    eligibilityResult: EligibilityResult,
    isSecondary: boolean = false
  ): Promise<boolean> {
    try {
      const updateField = isSecondary ? 'secondaryEligibilityDetails' : 'claimEligibilityDetails';
      
      await User.findByIdAndUpdate(userId, {
        [updateField]: eligibilityResult
      });

      console.log(`[INSURANCE-SERVICE] ${isSecondary ? 'Secondary' : 'Primary'} eligibility details updated for user:`, userId);
      return true;
    } catch (error) {
      console.error('[INSURANCE-SERVICE] Error updating eligibility details:', error);
      throw error;
    }
  }

  /**
   * Remove insurance record and update user
   */
  static async removeInsurance(
    userId: string,
    isPrimary: boolean = true
  ): Promise<boolean> {
    try {
      const user = await User.findById(userId) as IUser | null;
      if (!user) {
        throw new Error('User not found');
      }

      const insuranceIdField = isPrimary ? 'insuranceId' : 'secondaryInsuranceId';
      const insuranceId = (user as any)[insuranceIdField];

      if (insuranceId) {
        // Remove insurance record
        await Insurance.findByIdAndDelete(insuranceId);
        
        // Update user to remove insurance reference
        const updateData: any = {
          [insuranceIdField]: null
        };
        
        if (isPrimary) {
          updateData.claimEligibilityDetails = null;
        }
        
        await User.findByIdAndUpdate(userId, updateData);
      }

      console.log(`[INSURANCE-SERVICE] ${isPrimary ? 'Primary' : 'Secondary'} insurance removed for user:`, userId);
      return true;
    } catch (error) {
      console.error('[INSURANCE-SERVICE] Error removing insurance:', error);
      throw error;
    }
  }
} 