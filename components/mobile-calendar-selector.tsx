import React, { useState, useEffect } from "react"
import { format, addMonths, subMonths, startOfMonth, endOfMonth, eachDayOfInterval, isSameMonth, isSameDay, isBefore, startOfWeek, endOfWeek } from "date-fns"
import { ChevronLeft, ChevronRight } from "lucide-react"
import moment from "moment"
import { therapistApi } from '../lib/api'
import {
  dayOfWeekAsNumber,
  getDaysInMonth,
  convertUTCDateToLocalDate,
  convertUTCTimeToLocalTime,
  isDateInBlockedRange
} from "../utils/calendarUtils"

interface MobileCalendarSelectorProps {
  therapistName: string
  onDateSelect: (date: Date) => void
  onTimeSelect: (time: string) => void
  selectedDate: Date | null
  selectedTimeSlot: string | null
}

interface WorkingHours {
  day: string;
  startTime: string;
  endTime: string;
}

interface Appointment {
  start: string | Date;
  end: string | Date;
  status?: string;
}

interface TherapistData {
  workingHours: WorkingHours[];
  appointments: Appointment[];
  blockedDates?: { start: string; end: string }[];
}

export function MobileCalendarSelector({
  therapistName,
  onDateSelect,
  onTimeSelect,
  selectedDate,
  selectedTimeSlot,
}: MobileCalendarSelectorProps) {
  const [currentMonth, setCurrentMonth] = useState(new Date())
  const [therapistId, setTherapistId] = useState<string>("")
  const [therapistAvailableHours, setTherapistAvailableHours] = useState<any[]>([])
  const [allBlockDatesListInTherapist, setAllBlockDatesListInTherapist] = useState<any[]>([])
  const [allAppointmentListInTherapist, setAllAppointmentListInTherapist] = useState<any[]>([])
  const [availableDates, setAvailableDates] = useState<Date[]>([])
  const [availableTimeSlots, setAvailableTimeSlots] = useState<string[]>([])
  const [loadingDays, setLoadingDays] = useState(false)
  const [loadingSlots, setLoadingSlots] = useState(false)

  // Get therapist ID from localStorage
  useEffect(() => {
    const storedTherapist = localStorage.getItem("selectedTherapist");
    if (storedTherapist) {
      const therapist = JSON.parse(storedTherapist);
      const id = therapist._id || therapist.id || therapist.therapistId;
      if (id) {
        setTherapistId(id);
        console.log('[CALENDAR] Found therapist ID:', id);
      } else {
        console.log('[CALENDAR] No therapist ID found in:', therapist);
      }
    } else {
      console.log('[CALENDAR] No therapist selected');
    }
  }, []);

  // Fetch therapist data when therapist ID or month changes
  useEffect(() => {
    if (therapistId) {
      getTherapistAvailability();
    } else {
      // No therapist selected - clear all data
      setTherapistAvailableHours([]);
      setAllBlockDatesListInTherapist([]);
      setAllAppointmentListInTherapist([]);
      setAvailableDates([]);
      setAvailableTimeSlots([]);
    }
  }, [therapistId, currentMonth]);

  // Calculate available time slots when date is selected
  useEffect(() => {
    if (selectedDate && therapistAvailableHours.length) {
      calculateTimeSlotsForDate(selectedDate);
    }
  }, [selectedDate, therapistAvailableHours]);

  // Calculate available dates when therapist data changes
  useEffect(() => {
    if (therapistAvailableHours.length) {
      calculateAllAvailableDates(therapistAvailableHours, allBlockDatesListInTherapist, allAppointmentListInTherapist);
    }
  }, [therapistAvailableHours, allBlockDatesListInTherapist, allAppointmentListInTherapist, currentMonth]);

  const getTherapistAvailability = async () => {
    try {
      setLoadingDays(true);

      /* 1. master record (workingHours + blocked) */
      const details = await therapistApi.getTherapistDetails(therapistId);
      const workingHours: WorkingHours[] = details.workingHours ?? [];
      const blockedDates = details.blockedDates ?? [];

      /* 2. appointments for current + next 2 months */
      const month0 = new Date(currentMonth.getFullYear(), currentMonth.getMonth(), 1);
      const month1 = new Date(month0.getFullYear(), month0.getMonth() + 1, 1);
      const month2 = new Date(month0.getFullYear(), month0.getMonth() + 2, 1);

      const [a0, a1, a2] = await Promise.all([
        therapistApi.getTherapistAppointments(therapistId, month0.toISOString()),
        therapistApi.getTherapistAppointments(therapistId, month1.toISOString()),
        therapistApi.getTherapistAppointments(therapistId, month2.toISOString())
      ]);

      // Extract appointments from the AppointmentData responses
      const appointments0 = Array.isArray(a0) ? a0 : (a0 as any)?.data || [];
      const appointments1 = Array.isArray(a1) ? a1 : (a1 as any)?.data || [];
      const appointments2 = Array.isArray(a2) ? a2 : (a2 as any)?.data || [];

      const allAppointments: Appointment[] = [...appointments0, ...appointments1, ...appointments2].map((a: any) => ({
        ...a,
        start: new Date(a.start),
        end: new Date(a.end)
      }));

      /* 3. build available-hours blocks (handles overnight) */
      const avail: any[] = [];
      workingHours.forEach(h => {
        const dow = dayOfWeekAsNumber(h.day);
        const start = convertUTCTimeToLocalTime(h.startTime);  // Convert UTC to local time
        const end   = convertUTCTimeToLocalTime(h.endTime);    // Convert UTC to local time

        if (end > start) {
          avail.push({ startTime: start, endTime: end, daysOfWeek: [dow] });
        } else {                          // overnight shift split
          avail.push({ startTime: start, endTime: "23:59", daysOfWeek: [dow] });
          avail.push({ startTime: "00:00", endTime: end, daysOfWeek: [(dow + 1) % 7] });
        }
      });

      setTherapistAvailableHours(avail);
      setAllBlockDatesListInTherapist(blockedDates);
      setAllAppointmentListInTherapist(allAppointments);

      calculateAllAvailableDates(avail, blockedDates, allAppointments);
    } catch (err) {
      console.error(err);
      setAvailableDates([]);
    } finally {
      setLoadingDays(false);
    }
  };

  const calculateAllAvailableDates = (
    avail: any[],
    blocked: { start: string; end: string }[],
    appointments: Appointment[]
  ) => {
    const result: Date[] = [];
    const tomorrow = moment().add(1, "day").startOf("day");

    for (let off = 0; off < 3; off++) {
      const year  = currentMonth.getFullYear() + Math.floor((currentMonth.getMonth() + off) / 12);
      const month = (currentMonth.getMonth() + off) % 12;
      const dim   = getDaysInMonth(year, month);

      for (let d = 1; d <= dim; d++) {
        const date = new Date(year, month, d);
        if (moment(date).isBefore(tomorrow)) continue;
        if (moment(date).isAfter(moment().add(60, "days"))) continue;

        const dow = date.getDay();

        /* build time slots for that day using same logic as calculateTimeSlotsForDate */
        let slots: string[] = [];
        avail.filter(a => a.daysOfWeek[0] === dow).forEach(a => {
          try {
            // Parse times more robustly - handle formats like "21:30 PM", "1:00 AM", "23:00 PM"
            const startTimeMatch = a.startTime.match(/(\d{1,2}):(\d{2})/);
            const endTimeMatch = a.endTime.match(/(\d{1,2}):(\d{2})/);
            
            if (!startTimeMatch || !endTimeMatch) {
              console.warn('[CALENDAR] Invalid time format:', a.startTime, a.endTime);
              return;
            }
            
            const sh = parseInt(startTimeMatch[1]);
            const eh = parseInt(endTimeMatch[1]);
            
            for (let hr = sh; hr <= eh; hr++) {
              // For start hour, only add if startTime minute is 0
              if (hr === sh) {
                const startMinute = parseInt(startTimeMatch[2]);
                if (startMinute === 0) {
                  const timeSlot = moment({ hour: hr, minute: 0 }).format("h:mm A");
                  slots.push(timeSlot);
                }
              } else {
                // For non-start hours, always add the hour slot
                const timeSlot = moment({ hour: hr, minute: 0 }).format("h:mm A");
                slots.push(timeSlot);
              }

              // Add 30-minute slot if not the end hour, or if end hour has 30 minutes
              if (hr !== eh) {
                const timeSlot = moment({ hour: hr, minute: 30 }).format("h:mm A");
                slots.push(timeSlot);
              } else {
                const endMinute = parseInt(endTimeMatch[2]);
                if (endMinute === 30) {
                  const timeSlot = moment({ hour: hr, minute: 30 }).format("h:mm A");
                  slots.push(timeSlot);
                }
              }
            }
          } catch (error) {
            console.error('[CALENDAR] Error parsing time slot:', a, error);
          }
        });
        slots = [...new Set(slots)];   // dedupe

        // Filter slots to ensure each slot has a full 1-hour duration within working hours
        // This matches the logic in ClientDashboardCalendar
        console.log('[CALENDAR DEBUG] Initial slots before validation:', slots);
        
        const validSlots = slots.filter(slot => {
          const slotTime = moment(slot, "h:mm A");
          const slotEndTime = moment(slotTime).add(60, "minutes");

          // Check if both start and end time of the 1-hour slot are within working hours
          const slotStartInWorkingHours = slots.includes(slotTime.format("h:mm A"));
          const slotEndInWorkingHours = slots.includes(slotEndTime.format("h:mm A"));

          if (slot === "12:00 PM") {
            console.log(`[DEBUG] Validating slot ${slot}:`);
            console.log(`  slotTime: ${slotTime.format("h:mm A")}`);
            console.log(`  slotEndTime: ${slotEndTime.format("h:mm A")}`);
            console.log(`  slotStartInWorkingHours: ${slotStartInWorkingHours}`);
            console.log(`  slotEndInWorkingHours: ${slotEndInWorkingHours}`);
            console.log(`  includes 1:00 PM: ${slots.includes("1:00 PM")}`);
            console.log(`  result: ${slotStartInWorkingHours && slotEndInWorkingHours}`);
          }

          return slotStartInWorkingHours && slotEndInWorkingHours;
        });

        console.log('[CALENDAR DEBUG] Valid slots after validation:', validSlots);
        slots = validSlots;

        /* filter against blocked + appointments */
        slots = slots.filter(s => {
          const t = moment(s,"h:mm A");

          // Create probe in local timezone
          const slotStart = moment(date).hours(t.hours()).minutes(t.minutes()).seconds(0).milliseconds(0);
          const slotEnd = slotStart.clone().add(60, "minutes"); // 1-hour appointment duration

          const blk = blocked.some(r => {
            // Convert blocked dates to local timezone for comparison
            const blockStart = moment(r.start).local().startOf('minute');
            const blockEnd = moment(r.end).local().startOf('minute');
            
            // Check if slot overlaps with blocked time
            // Overlap if: slotStart < blockEnd AND slotEnd > blockStart
            const hasOverlap = slotStart.isBefore(blockEnd) && slotEnd.isAfter(blockStart);

            if (s === "12:00 PM") {
              console.log(`[DEBUG] Checking slot ${s} (${slotStart.format('HH:mm')} - ${slotEnd.format('HH:mm')}):`);
              console.log(`  Block: ${blockStart.format('YYYY-MM-DD HH:mm:ss')} - ${blockEnd.format('YYYY-MM-DD HH:mm:ss')}`);
              console.log(`  slotStart exact: ${slotStart.format('YYYY-MM-DD HH:mm:ss.SSS')}`);
              console.log(`  blockEnd exact: ${blockEnd.format('YYYY-MM-DD HH:mm:ss.SSS')}`);
              console.log(`  slotStart timestamp: ${slotStart.valueOf()}`);
              console.log(`  blockEnd timestamp: ${blockEnd.valueOf()}`);
              console.log(`  Overlap check: slotStart < blockEnd (${slotStart.isBefore(blockEnd)}) AND slotEnd > blockStart (${slotEnd.isAfter(blockStart)})`);
              console.log(`  Result: ${hasOverlap}`);
            }

            return hasOverlap;
          });
          
          const apt = appointments.some(a => {
            // Convert appointment times to local timezone for comparison
            const aptStart = moment(a.start).local();
            const aptEnd = moment(a.end).local();
            
            // Check if slot overlaps with appointment time
            // Overlap if: slotStart < aptEnd AND slotEnd > aptStart
            const hasOverlap = slotStart.isBefore(aptEnd) && slotEnd.isAfter(aptStart);
            
            return hasOverlap;
          });

          if (s === "12:00 PM") {
            console.log(`[DEBUG] Final result for slot ${s}: blocked=${blk}, appointment=${apt}, available=${!blk && !apt}`);
          }

          return !blk && !apt;
        });

        if (slots.length) result.push(new Date(date));
      }
    }
    setAvailableDates(result);
  };

  const calculateTimeSlotsForDate = (date: Date) => {
    console.log('[CALENDAR DEBUG] ===== calculateTimeSlotsForDate START =====');
    console.log('[CALENDAR DEBUG] Input date:', date);
    console.log('[CALENDAR DEBUG] Date string:', date.toISOString());
    console.log('[CALENDAR DEBUG] Date local string:', date.toDateString());

    setLoadingSlots(true);
    try {
      const dow = date.getDay();
      console.log('[CALENDAR DEBUG] Day of week (0=Sun, 6=Sat):', dow);

      let slots: string[] = [];
      console.log('[CALENDAR DEBUG] therapistAvailableHours:', therapistAvailableHours);
      console.log('[CALENDAR DEBUG] therapistAvailableHours length:', therapistAvailableHours.length);

      const filteredHours = therapistAvailableHours.filter(h => h.daysOfWeek[0] === dow);
      console.log('[CALENDAR DEBUG] Filtered hours for this day:', filteredHours);

      therapistAvailableHours
        .filter(h => h.daysOfWeek[0] === dow)
        .forEach((h) => {
          try {
            // Parse times more robustly - handle formats like "21:30 PM", "1:00 AM", "23:00 PM"
            const startTimeMatch = h.startTime.match(/(\d{1,2}):(\d{2})/);
            const endTimeMatch = h.endTime.match(/(\d{1,2}):(\d{2})/);

            if (!startTimeMatch || !endTimeMatch) {
              console.warn('Invalid time format:', h.startTime, h.endTime);
              return;
            }

            const sh = parseInt(startTimeMatch[1]);
            const eh = parseInt(endTimeMatch[1]);

            for (let hr = sh; hr <= eh; hr++) {
              // For start hour, only add if startTime minute is 0
              if (hr === sh) {
                const startMinute = parseInt(startTimeMatch[2]);
                if (startMinute === 0) {
                  const timeSlot = moment({ hour: hr, minute: 0 }).format("h:mm A");
                  slots.push(timeSlot);
                }
              } else {
                // For non-start hours, always add the hour slot
                const timeSlot = moment({ hour: hr, minute: 0 }).format("h:mm A");
                slots.push(timeSlot);
              }

              // Add 30-minute slot if not the end hour, or if end hour has 30 minutes
              if (hr !== eh) {
                const timeSlot = moment({ hour: hr, minute: 30 }).format("h:mm A");
                slots.push(timeSlot);
              } else {
                const endMinute = parseInt(endTimeMatch[2]);
                if (endMinute === 30) {
                  const timeSlot = moment({ hour: hr, minute: 30 }).format("h:mm A");
                  slots.push(timeSlot);
                }
              }
            }
          } catch (error) {
            console.error('Error parsing time slot:', h, error);
          }
        });

      // Filter slots to ensure each slot has a full 1-hour duration within working hours
      // This matches the logic in ClientDashboardCalendar
      console.log('[CALENDAR DEBUG] Initial slots before validation:', slots);
      
      const validSlots = slots.filter(slot => {
        const slotTime = moment(slot, "h:mm A");
        const slotEndTime = moment(slotTime).add(60, "minutes");

        // Check if both start and end time of the 1-hour slot are within working hours
        const slotStartInWorkingHours = slots.includes(slotTime.format("h:mm A"));
        const slotEndInWorkingHours = slots.includes(slotEndTime.format("h:mm A"));

        if (slot === "12:00 PM") {
          console.log(`[DEBUG] Validating slot ${slot}:`);
          console.log(`  slotTime: ${slotTime.format("h:mm A")}`);
          console.log(`  slotEndTime: ${slotEndTime.format("h:mm A")}`);
          console.log(`  slotStartInWorkingHours: ${slotStartInWorkingHours}`);
          console.log(`  slotEndInWorkingHours: ${slotEndInWorkingHours}`);
          console.log(`  includes 1:00 PM: ${slots.includes("1:00 PM")}`);
          console.log(`  result: ${slotStartInWorkingHours && slotEndInWorkingHours}`);
        }

        return slotStartInWorkingHours && slotEndInWorkingHours;
      });

      console.log('[CALENDAR DEBUG] Valid slots after validation:', validSlots);
      slots = validSlots;

      /* Convert appointments to local timezone for comparison */
      const dayApts = allAppointmentListInTherapist.filter(a => {
        // Convert UTC appointment time to local timezone for day comparison
        const appointmentLocalDate = moment(a.start).local();
        const inputLocalDate = moment(date);
        return appointmentLocalDate.isSame(inputLocalDate, "day");
      });

      slots = slots.filter((s) => {
        const t = moment(s,"h:mm A");

        // Create probe in local timezone
        const slotStart = moment(date).hours(t.hours()).minutes(t.minutes()).seconds(0).milliseconds(0);
        const slotEnd = slotStart.clone().add(60, "minutes"); // 1-hour appointment duration

        const blk = allBlockDatesListInTherapist.some((r) => {
          // Convert blocked dates to local timezone for comparison
          const blockStart = moment(r.start).local().startOf('minute');
          const blockEnd = moment(r.end).local().startOf('minute');
          
          // Check if slot overlaps with blocked time
          // Overlap if: slotStart < blockEnd AND slotEnd > blockStart
          const hasOverlap = slotStart.isBefore(blockEnd) && slotEnd.isAfter(blockStart);

          if (s === "12:00 PM") {
            console.log(`[DEBUG] Checking slot ${s} (${slotStart.format('HH:mm')} - ${slotEnd.format('HH:mm')}):`);
            console.log(`  Block: ${blockStart.format('YYYY-MM-DD HH:mm:ss')} - ${blockEnd.format('YYYY-MM-DD HH:mm:ss')}`);
            console.log(`  slotStart exact: ${slotStart.format('YYYY-MM-DD HH:mm:ss.SSS')}`);
            console.log(`  blockEnd exact: ${blockEnd.format('YYYY-MM-DD HH:mm:ss.SSS')}`);
            console.log(`  slotStart timestamp: ${slotStart.valueOf()}`);
            console.log(`  blockEnd timestamp: ${blockEnd.valueOf()}`);
            console.log(`  Overlap check: slotStart < blockEnd (${slotStart.isBefore(blockEnd)}) AND slotEnd > blockStart (${slotEnd.isAfter(blockStart)})`);
            console.log(`  Result: ${hasOverlap}`);
          }

          return hasOverlap;
        });

        const apt = dayApts.some((a) => {
          // Convert appointment times to local timezone for comparison
          const aptStart = moment(a.start).local();
          const aptEnd = moment(a.end).local();
          
          // Check if slot overlaps with appointment time
          // Overlap if: slotStart < aptEnd AND slotEnd > aptStart
          const hasOverlap = slotStart.isBefore(aptEnd) && slotEnd.isAfter(aptStart);
          
          return hasOverlap;
        });

        if (s === "12:00 PM") {
          console.log(`[DEBUG] Final result for slot ${s}: blocked=${blk}, appointment=${apt}, available=${!blk && !apt}`);
        }

        return !blk && !apt;
      });

      // Essential logs for debugging
      const workingHoursForDay = therapistAvailableHours.filter(h => h.daysOfWeek[0] === dow);
      console.log(`[CALENDAR] ${date.toDateString()} - Working hours details:`,
        workingHoursForDay.map(h => `${h.startTime} - ${h.endTime}`)
      );
      console.log(`[CALENDAR] ${date.toDateString()} - Appointments details:`,
        dayApts.map(a => `${moment(a.start).local().format('HH:mm')} - ${moment(a.end).local().format('HH:mm')}`)
      );
      console.log(`[CALENDAR] ${date.toDateString()} - Blocked dates details:`,
        allBlockDatesListInTherapist.map(b => `${moment(b.start).local().format('MM/DD HH:mm')} - ${moment(b.end).local().format('MM/DD HH:mm')}`)
      );
      console.log(`[CALENDAR] ${date.toDateString()} - Appointments: ${dayApts.length}, Blocked dates: ${allBlockDatesListInTherapist.length}`);
      console.log(`[CALENDAR] ${date.toDateString()} - Available slots:`, slots);

      setAvailableTimeSlots(slots);
    } catch (err) {
      console.error('[CALENDAR] Error in calculateTimeSlotsForDate:', err);
      setAvailableTimeSlots([]);
    } finally {
      setLoadingSlots(false);
    }
  };

  const isDateAvailable = (date: Date) => {
    return availableDates.some(availableDate => 
      isSameDay(availableDate, date)
    );
  };

  const isDateInPast = (date: Date) => {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    return isBefore(date, today);
  };

  // Generate calendar grid - show full weeks
    const monthStart = startOfMonth(currentMonth);
    const monthEnd = endOfMonth(currentMonth);
  const calendarStart = startOfWeek(monthStart);
  const calendarEnd = endOfWeek(monthEnd);
  const calendarDays = eachDayOfInterval({ start: calendarStart, end: calendarEnd });

    return (
    <div className="bg-white rounded-lg shadow-sm border">
      {/* Calendar Header */}
      <div className="flex items-center justify-between p-4 border-b">
          <button
          onClick={() => setCurrentMonth(subMonths(currentMonth, 1))}
          className="p-2 hover:bg-gray-100 rounded-lg"
          >
          <ChevronLeft className="w-5 h-5" />
          </button>
        <h2 className="text-lg font-semibold">
          {format(currentMonth, "MMMM yyyy")}
        </h2>
        <button
          onClick={() => setCurrentMonth(addMonths(currentMonth, 1))}
          className="p-2 hover:bg-gray-100 rounded-lg"
        >
          <ChevronRight className="w-5 h-5" />
          </button>
        </div>

      {/* Calendar Grid */}
      <div className="p-4">
        {/* Day headers */}
        <div className="grid grid-cols-7 gap-1 mb-2">
          {["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"].map((day) => (
            <div key={day} className="text-center text-sm font-medium text-gray-500 py-2">
              {day}
            </div>
          ))}
        </div>

        {/* Calendar days */}
        <div className="grid grid-cols-7 gap-1">
          {calendarDays.map((date) => {
            const isCurrentMonth = isSameMonth(date, currentMonth)
            const isSelected = selectedDate && isSameDay(date, selectedDate)
            const isAvailable = isDateAvailable(date)
            const isPast = isDateInPast(date)
            const isDisabled = !isCurrentMonth || isPast

            return (
              <button
                key={date.toISOString()}
                onClick={() => {
                  if (!isDisabled) {
                    onDateSelect(date);
                  }
                }}
                disabled={isDisabled}
                className={`
                  aspect-square p-2 text-sm rounded-lg transition-colors relative
                  ${isSelected
                    ? "bg-orange-500 text-white"
                    : isAvailable && !isPast && isCurrentMonth
                    ? "bg-orange-100 text-orange-700 hover:bg-orange-200 border border-orange-300"
                    : isCurrentMonth && !isPast
                    ? "text-gray-700 hover:bg-gray-100"
                    : isCurrentMonth && isPast
                    ? "text-gray-400 cursor-not-allowed"
                    : "text-gray-300 cursor-not-allowed"
                  }
                `}
              >
                {format(date, "d")}
                {isAvailable && isCurrentMonth && !isPast && (
                  <div className="absolute bottom-1 left-1/2 transform -translate-x-1/2 w-1 h-1 bg-orange-500 rounded-full"></div>
                )}
              </button>
            )
          })}
        </div>
      </div>

      {/* Loading state */}
      {loadingDays && (
        <div className="flex justify-center py-4 border-t">
          <div className="text-gray-500">Loading available dates...</div>
        </div>
      )}

      {/* Time slots section */}
      {selectedDate && !loadingDays && (
        <div className="border-t">
          <div className="p-4 bg-gray-50">
            <h3 className="text-sm font-medium text-gray-700 mb-3">
              Available times for {format(selectedDate, "EEEE, MMMM d")}
            </h3>

          {loadingSlots ? (
              <div className="flex justify-center py-4">
                <div className="text-gray-500">Loading available times...</div>
              </div>
            ) : availableTimeSlots.length === 0 ? (
              <div className="flex justify-center py-4">
                <div className="text-gray-500">No available times for this date</div>
            </div>
          ) : (
              <div className="grid grid-cols-2 gap-2">
                {availableTimeSlots.map((time, index) => (
                  <button
                    key={index}
                    onClick={() => onTimeSelect(time)}
                    className={`p-3 rounded-lg border text-sm font-medium transition-colors ${
                      selectedTimeSlot === time
                        ? "bg-orange-500 text-white border-orange-500"
                        : "bg-white text-gray-700 border-gray-300 hover:border-orange-300 hover:bg-orange-50"
                    }`}
                  >
                    {time}
                  </button>
                ))}
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  )
}